<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome标签页管理器测试</title>
  <style>
    html {
      width: 420px;
      height: 600px;
      min-width: 420px;
      min-height: 600px;
    }
    
    body {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
      font-size: 14px;
      line-height: 1.5;
      color: #202124;
      background-color: #ffffff;
      width: 420px;
      height: 600px;
      min-width: 420px;
      min-height: 600px;
      overflow: hidden;
    }
    
    .test-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      padding: 16px;
    }
    
    .test-header {
      background: #1a73e8;
      color: white;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .test-content {
      flex: 1;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      overflow-y: auto;
    }
    
    .test-item {
      background: white;
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #e8eaed;
    }
    
    .test-footer {
      background: #5f6368;
      color: white;
      padding: 12px;
      border-radius: 8px;
      margin-top: 16px;
      text-align: center;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <div class="test-header">
      <h1>Chrome标签页管理器</h1>
      <p>测试版本 - 尺寸验证</p>
    </div>
    
    <div class="test-content">
      <div class="test-item">
        <strong>当前尺寸:</strong> 420px × 600px
      </div>
      <div class="test-item">
        <strong>窗口数量:</strong> 3
      </div>
      <div class="test-item">
        <strong>标签页数量:</strong> 15
      </div>
      <div class="test-item">
        <strong>内存节省:</strong> 65%
      </div>
      <div class="test-item">
        <strong>状态:</strong> 正常运行
      </div>
      <div class="test-item">
        <strong>测试项目:</strong>
        <ul>
          <li>CSS样式加载 ✓</li>
          <li>尺寸设置 ✓</li>
          <li>字体渲染 ✓</li>
          <li>布局显示 ✓</li>
        </ul>
      </div>
    </div>
    
    <div class="test-footer">
      如果您能看到这个完整的界面，说明popup尺寸问题已经解决
    </div>
  </div>
  
  <script>
    // 简单的测试脚本
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Popup test loaded successfully');
      console.log('Window size:', window.innerWidth, 'x', window.innerHeight);
      console.log('Document size:', document.documentElement.clientWidth, 'x', document.documentElement.clientHeight);
    });
  </script>
</body>
</html>
