/* Chrome标签页管理器 - 现代化UI设计 */

/* CSS变量定义 */
:root {
  --primary-color: #1a73e8;
  --primary-hover: #1557b0;
  --primary-light: #e8f0fe;
  --secondary-color: #5f6368;
  --success-color: #137333;
  --warning-color: #f9ab00;
  --danger-color: #d93025;
  --info-color: #1976d2;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --surface-hover: #f1f3f4;
  --border-color: #e8eaed;
  --border-focus: #1a73e8;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-muted: #9aa0a6;
  --text-inverse: #ffffff;
  --shadow-light: 0 1px 3px rgba(60, 64, 67, 0.3);
  --shadow-medium: 0 2px 6px rgba(60, 64, 67, 0.3);
  --shadow-heavy: 0 8px 24px rgba(60, 64, 67, 0.3);
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 12px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;
  --transition-fast: 0.15s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.45s ease;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  width: 420px;
  height: 600px;
  min-width: 420px;
  min-height: 600px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  width: 420px;
  height: 600px;
  min-width: 420px;
  min-height: 600px;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 600px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* 现代化工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  flex-shrink: 0;
  position: relative;
}

.toolbar::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 应用Logo */
.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  margin: 0;
}

.app-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

/* 搜索容器增强 */
.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  transition: all var(--transition-medium);
}

.search-input-wrapper:hover {
  background-color: var(--surface-hover);
  border-color: var(--secondary-color);
}

.search-input-wrapper:focus-within {
  background-color: var(--background-color);
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  z-index: 1;
  pointer-events: none;
}

.search-input {
  border: none;
  outline: none;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) var(--spacing-xl);
  font-size: var(--font-size-sm);
  width: 180px;
  background-color: transparent;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-btn {
  border: none;
  border-left: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  transition: all var(--transition-fast);
}

.search-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  transform: scale(1.05);
}

/* 现代化按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  gap: var(--spacing-xs);
  font-family: var(--font-family);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all var(--transition-medium);
  transform: translate(-50%, -50%);
}

.btn:hover::before {
  width: 100%;
  height: 100%;
}

.btn:hover {
  background-color: var(--surface-hover);
  border-color: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

.btn-icon {
  padding: var(--spacing-sm);
  min-width: 36px;
  min-height: 36px;
  border-radius: var(--border-radius);
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  min-height: 28px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  border-color: var(--primary-hover);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--surface-color);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #b71c1c);
  color: var(--text-inverse);
  border-color: var(--danger-color);
  box-shadow: var(--shadow-light);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #b71c1c, var(--danger-color));
  border-color: #b71c1c;
  transform: translateY(-2px);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--surface-color);
  color: var(--text-primary);
}

.btn-refresh {
  background: linear-gradient(135deg, var(--success-color), #0f5132);
  color: var(--text-inverse);
  border-color: var(--success-color);
}

.btn-refresh:hover {
  background: linear-gradient(135deg, #0f5132, var(--success-color));
  border-color: #0f5132;
}

/* 现代化统计信息栏 */
.stats-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  position: relative;
}

.stats-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-medium);
  min-width: 80px;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  transform: scaleX(0);
  transition: transform var(--transition-medium);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.stat-item:hover::before {
  transform: scaleX(1);
}

.stat-icon {
  font-size: var(--font-size-lg);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-md);
  line-height: 1.2;
}

/* 现代化批量操作工具栏 */
.batch-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(26, 115, 232, 0.1) 100%);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  position: relative;
  animation: slideDown var(--transition-medium) ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.batch-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
}

.batch-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.batch-icon {
  font-size: var(--font-size-md);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.batch-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 现代化加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: var(--spacing-lg);
}

.loading-animation {
  position: relative;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.1s;
  border-top-color: var(--success-color);
  width: 75%;
  height: 75%;
  top: 12.5%;
  left: 12.5%;
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.2s;
  border-top-color: var(--warning-color);
  width: 50%;
  height: 50%;
  top: 25%;
  left: 25%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.5px;
}

/* 现代化空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: var(--spacing-lg);
  position: relative;
}

.empty-animation {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.empty-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: particle-float 2s infinite ease-in-out;
}

.particle:nth-child(1) {
  top: 10px;
  left: 10px;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 10px;
  right: 10px;
  animation-delay: 0.5s;
  background: var(--success-color);
}

.particle:nth-child(3) {
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 1s;
  background: var(--warning-color);
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-15px);
    opacity: 1;
  }
}

.empty-content {
  text-align: center;
  max-width: 280px;
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.empty-action {
  margin-top: var(--spacing-md);
}

/* 标签页列表样式 */
.tab-list {
  background-color: var(--background-color);
}

.tab-list.collapsed {
  display: none;
}

/* 标签页备注和分类样式 */
.tab-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.tab-item:last-child {
  border-bottom: none;
}

.tab-item:hover {
  background-color: var(--surface-color);
}

.tab-item.selected {
  background-color: #e8f0fe;
  border-left: 3px solid var(--primary-color);
}

.tab-item.active {
  background-color: #fff3e0;
}

.tab-item.bookmarked {
  background-color: #f0f8ff;
}

.tab-item.archived {
  opacity: 0.6;
  background-color: #f5f5f5;
}

/* 标签页复选框 */
.tab-checkbox {
  margin-right: var(--spacing-sm);
  cursor: pointer;
}

/* 标签页图标 */
.tab-favicon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  border-radius: 2px;
}

.tab-favicon.placeholder {
  background-color: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--text-secondary);
}

/* 标签页主要信息 */
.tab-info {
  flex: 1;
  min-width: 0;
  margin-right: var(--spacing-sm);
}

.tab-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.tab-url {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.tab-notes-preview {
  font-size: 11px;
  color: var(--text-muted);
  font-style: italic;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签页元数据显示 */
.tab-metadata {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-right: var(--spacing-sm);
}

.tab-category-indicator {
  width: 3px;
  height: 40px;
  border-radius: 2px;
  flex-shrink: 0;
}

.tab-priority-indicator {
  display: flex;
  align-items: center;
  font-size: 10px;
  color: var(--warning-color);
}

.tab-rating-indicator {
  display: flex;
  align-items: center;
  font-size: 10px;
  color: var(--warning-color);
}

.tab-bookmark-indicator {
  color: var(--primary-color);
  font-size: 12px;
}

.tab-tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 2px;
}

.tab-tag-chip {
  background-color: var(--surface-color);
  color: var(--text-secondary);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  border: 1px solid var(--border-color);
}

/* 标签页操作按钮 */
.tab-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.tab-action-btn {
  padding: 2px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 2px;
  color: var(--text-secondary);
  font-size: 12px;
  transition: all 0.2s ease;
}

.tab-action-btn:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

/* 标签页编辑对话框 */
.tab-edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-content {
  position: relative;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
}

.dialog-footer {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
}

.form-row .form-group {
  flex: 1;
}

/* 标签页预览 */
.tab-edit-preview {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  margin-bottom: var(--spacing-md);
}

.tab-edit-preview .tab-favicon {
  width: 20px;
  height: 20px;
}

.tab-edit-preview .tab-info {
  flex: 1;
  margin-right: 0;
}

.tab-edit-preview .tab-title {
  font-size: 14px;
  margin-bottom: 2px;
}

.tab-edit-preview .tab-url {
  font-size: 12px;
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.category-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-color);
}

.category-item:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.category-item.selected {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.category-name {
  font-size: 12px;
  font-weight: 500;
}

/* 标签输入 */
.tag-input-container {
  position: relative;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius-small) var(--border-radius-small);
  z-index: 1000;
  max-height: 150px;
  overflow-y: auto;
  display: none;
}

.tag-suggestion-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tag-suggestion-item:hover {
  background-color: var(--surface-color);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--surface-color);
  color: var(--text-primary);
  padding: 2px 6px;
  border-radius: var(--border-radius-small);
  font-size: 12px;
  border: 1px solid var(--border-color);
}

.tag-remove {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  margin-left: 2px;
}

.tag-remove:hover {
  color: var(--danger-color);
}

/* 评分输入 */
.rating-input {
  display: flex;
  gap: 2px;
}

.rating-star {
  font-size: 16px;
  color: var(--border-color);
  cursor: pointer;
  transition: color 0.2s ease;
}

.rating-star:hover,
.rating-star.active {
  color: var(--warning-color);
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  color: var(--text-primary);
}

/* 窗口组样式 */
.window-group {
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--background-color);
  box-shadow: var(--shadow-light);
}

.window-group.focused {
  border-color: var(--primary-color);
}

/* 窗口组头部 */
.window-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  user-select: none;
}

.window-group-header:hover {
  background-color: #f1f3f4;
}

.window-group-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.window-group-title {
  font-weight: 500;
  font-size: 14px;
  color: var(--text-primary);
}

.window-group-count {
  font-size: 12px;
  color: var(--text-secondary);
  background-color: var(--border-color);
  padding: 2px 6px;
  border-radius: 10px;
}

.window-group-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.collapse-icon {
  transition: transform 0.2s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

/* 标签页列表 */
.tab-list {
  background-color: var(--background-color);
}

.tab-list.collapsed {
  display: none;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.tab-item:last-child {
  border-bottom: none;
}

.tab-item:hover {
  background-color: var(--surface-color);
}

.tab-item.selected {
  background-color: #e8f0fe;
  border-left: 3px solid var(--primary-color);
}

.tab-item.active {
  background-color: #fff3e0;
}

/* 标签页复选框 */
.tab-checkbox {
  margin-right: var(--spacing-sm);
  cursor: pointer;
}

/* 标签页图标 */
.tab-favicon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  border-radius: 2px;
}

.tab-favicon.placeholder {
  background-color: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--text-secondary);
}

/* 标签页信息 */
.tab-info {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.tab-url {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

/* 标签页操作按钮 */
.tab-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.tab-action-btn {
  padding: 2px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 2px;
  color: var(--text-secondary);
  font-size: 12px;
  transition: all 0.2s ease;
}

.tab-action-btn:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

/* 右键菜单 */
.context-menu {
  position: absolute;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-xs) 0;
  min-width: 160px;
  z-index: 1000;
}

.menu-item {
  padding: var(--spacing-xs) var(--spacing-md);
  cursor: pointer;
  font-size: 13px;
  color: var(--text-primary);
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: var(--surface-color);
}

.menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.modal-body {
  padding: var(--spacing-md);
}

.modal-footer {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

/* 状态栏 */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
  font-size: 11px;
  color: var(--text-secondary);
  flex-shrink: 0;
}

/* 拖拽排序样式 */
.tab-item[draggable="true"] {
  cursor: move;
}

.tab-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  transition: all 0.2s ease;
}

.window-group-header[draggable="true"] {
  cursor: move;
}

.window-group.dragging {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

/* 拖拽指示器 */
.drop-indicator {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 1px;
  box-shadow: 0 0 4px rgba(26, 115, 232, 0.5);
}

.drop-line {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 1px;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; transform: scaleY(0.5); }
  50% { opacity: 1; transform: scaleY(1); }
  100% { opacity: 0.5; transform: scaleY(0.5); }
}

/* 拖拽悬停状态 */
.tab-item.drag-hover {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-small);
}

.window-group.drag-hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(26, 115, 232, 0.3);
}

/* 拖拽时的滚动提示 */
.drag-scroll-hint {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000;
  pointer-events: none;
}

/* 标签页状态指示器 */
.tab-status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  flex-shrink: 0;
}

.tab-status-indicator.active {
  background-color: var(--success-color);
  box-shadow: 0 0 4px rgba(19, 115, 51, 0.5);
}

.tab-status-indicator.suspended {
  background-color: var(--warning-color);
  box-shadow: 0 0 4px rgba(249, 171, 0, 0.5);
}

.tab-status-indicator.loading {
  background-color: var(--primary-color);
  animation: loading-pulse 1s infinite;
}

@keyframes loading-pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* 重复标签页指示器 */
.tab-duplicate-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background-color: var(--danger-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  z-index: 1;
}

.tab-item.has-duplicates {
  position: relative;
}

.tab-item.has-duplicates::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--warning-color);
  border-radius: var(--border-radius-small);
  pointer-events: none;
}

/* 内存使用指示器 */
.memory-usage-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-secondary);
}

.memory-bar {
  width: 40px;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.memory-bar-fill {
  height: 100%;
  background-color: var(--success-color);
  transition: width 0.3s ease;
}

.memory-bar-fill.medium {
  background-color: var(--warning-color);
}

.memory-bar-fill.high {
  background-color: var(--danger-color);
}

/* 操作撤销通知 */
.undo-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-primary);
  color: white;
  padding: 12px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.undo-notification .undo-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.undo-notification .undo-btn:hover {
  background-color: var(--primary-hover);
}

/* 会话管理面板 */
.session-panel {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100%;
  background-color: var(--background-color);
  border-left: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
  z-index: 1500;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.session-panel.open {
  right: 0;
}

.session-panel-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.session-panel-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.session-list {
  padding: var(--spacing-md);
}

.session-item {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.session-item-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.session-item-info {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 标签页预览 */
.tab-preview {
  position: absolute;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-md);
  min-width: 200px;
  max-width: 300px;
  z-index: 1000;
  display: none;
  animation: fadeIn 0.2s ease;
}

.tab-preview.visible {
  display: block;
}

.tab-preview-screenshot {
  width: 100%;
  height: 120px;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
}

.tab-preview-screenshot img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tab-preview-info {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.tab-preview-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.tab-preview-url {
  word-break: break-all;
  margin-bottom: 4px;
}

.tab-preview-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
}

/* 增强的统计信息 */
.enhanced-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
}

.stat-item-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.stat-value-enhanced {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.stat-label-enhanced {
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
}

.stat-change {
  font-size: 10px;
  font-weight: 500;
  margin-top: 2px;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

/* 响应式调整 */
@media (max-width: 400px) {
  .session-panel {
    width: 100%;
    right: -100%;
  }
  
  .enhanced-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tab-preview {
    max-width: 250px;
  }
}

/* 滚动条样式 */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: var(--surface-color);
}

.content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* 图标样式 */
.icon {
  font-size: 14px;
  line-height: 1;
}

/* 动画效果 */
.window-group {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* 主内容区域 */
.content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--background-color);
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: transparent;
}

.content::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 3px;
  transition: background-color var(--transition-fast);
}

.content::-webkit-scrollbar-thumb:hover {
  background-color: var(--secondary-color);
}

.window-groups {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  min-height: 100%;
}

/* 窗口组现代化样式 */
.window-group {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: all var(--transition-medium);
  position: relative;
}

.window-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.window-group:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.window-group:hover::before {
  opacity: 1;
}

.window-group.focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.window-group.focused::before {
  opacity: 1;
}

.window-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  user-select: none;
  position: relative;
}

.window-group-header:hover {
  background: linear-gradient(135deg, var(--surface-hover) 0%, var(--surface-color) 100%);
}

.window-group-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.window-group-title {
  font-weight: 600;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  line-height: 1.2;
}

.window-group-count {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background-color: var(--surface-color);
  padding: 2px 8px;
  border-radius: var(--border-radius-large);
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.window-group-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.window-group-header:hover .window-group-actions {
  opacity: 1;
}

.collapse-icon {
  font-size: var(--font-size-md);
  transition: transform var(--transition-medium);
  color: var(--text-secondary);
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

/* 标签页列表现代化样式 */
.tab-list {
  background-color: var(--background-color);
  transition: all var(--transition-medium);
}

.tab-list.collapsed {
  display: none;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  background-color: var(--background-color);
}

.tab-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, var(--primary-color), var(--success-color));
  transform: scaleY(0);
  transition: transform var(--transition-medium);
}

.tab-item:hover {
  background-color: var(--surface-color);
  transform: translateX(4px);
}

.tab-item:hover::before {
  transform: scaleY(1);
}

.tab-item.selected {
  background: linear-gradient(135deg, rgba(26, 115, 232, 0.1), rgba(26, 115, 232, 0.05));
  border-left: 3px solid var(--primary-color);
}

.tab-item.selected::before {
  transform: scaleY(1);
}

.tab-item.active {
  background: linear-gradient(135deg, rgba(52, 168, 83, 0.1), rgba(52, 168, 83, 0.05));
  border-left: 3px solid var(--success-color);
}

.tab-item.bookmarked {
  background: linear-gradient(135deg, rgba(251, 188, 4, 0.1), rgba(251, 188, 4, 0.05));
}

.tab-item.archived {
  opacity: 0.6;
  background-color: var(--surface-color);
}

.tab-item:last-child {
  border-bottom: none;
}

/* 标签页复选框 */
.tab-checkbox {
  margin-right: var(--spacing-sm);
  cursor: pointer;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  transition: all var(--transition-fast);
  position: relative;
}

.tab-checkbox:hover {
  border-color: var(--primary-color);
}

.tab-checkbox:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tab-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* 标签页图标增强 */
.tab-favicon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  border-radius: var(--border-radius-small);
  transition: transform var(--transition-fast);
}

.tab-item:hover .tab-favicon {
  transform: scale(1.1);
}

.tab-favicon.placeholder {
  background-color: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

/* 标签页主要信息 */
.tab-info {
  flex: 1;
  min-width: 0;
  margin-right: var(--spacing-sm);
}

.tab-title {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin-bottom: 2px;
}

.tab-url {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.tab-notes-preview {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  font-style: italic;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签页元数据现代化 */
.tab-metadata {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-right: var(--spacing-sm);
}

.tab-category-indicator {
  width: 3px;
  height: 32px;
  border-radius: 2px;
  flex-shrink: 0;
  background: linear-gradient(to bottom, var(--primary-color), var(--success-color));
}

.tab-priority-indicator {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--warning-color);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.tab-rating-indicator {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--warning-color);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.tab-bookmark-indicator {
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  animation: pulse 2s infinite;
}

.tab-tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 2px;
}

.tab-tag-chip {
  background: linear-gradient(135deg, var(--surface-color), var(--background-color));
  color: var(--text-secondary);
  padding: 1px 6px;
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.tab-tag-chip:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  transform: scale(1.05);
}

/* 标签页操作按钮现代化 */
.tab-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.tab-action-btn {
  padding: var(--spacing-xs);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--border-radius-small);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-action-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
  transform: scale(1.1);
}

.tab-action-btn.danger:hover {
  background-color: var(--danger-color);
  color: white;
}

/* 现代化右键菜单 */
.context-menu {
  position: absolute;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  padding: var(--spacing-xs) 0;
  min-width: 200px;
  z-index: 2000;
  backdrop-filter: blur(8px);
  animation: fadeIn var(--transition-fast) ease-out;
}

.context-menu::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 20px;
  width: 10px;
  height: 10px;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  position: relative;
  gap: var(--spacing-sm);
}

.menu-item:hover {
  background-color: var(--surface-color);
  color: var(--primary-color);
}

.menu-item.menu-item-danger:hover {
  background-color: var(--danger-color);
  color: white;
}

.menu-icon {
  font-size: var(--font-size-md);
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.menu-text {
  flex: 1;
  font-weight: 500;
}

.menu-shortcut {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  background-color: var(--surface-color);
  padding: 2px 6px;
  border-radius: var(--border-radius-small);
  font-family: monospace;
}

.menu-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  margin: var(--spacing-xs) 0;
}

/* 现代化模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn var(--transition-medium) ease-out;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--background-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  animation: scaleIn var(--transition-medium) ease-out;
  border: 1px solid var(--border-color);
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--surface-color), var(--background-color));
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background-color: var(--surface-hover);
  transform: scale(1.1);
}

.modal-body {
  padding: var(--spacing-md);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--surface-color), var(--background-color));
  border-top: 1px solid var(--border-color);
}

/* 现代化状态栏 */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xs) var(--spacing-md);
  background: linear-gradient(135deg, var(--surface-color), var(--background-color));
  border-top: 1px solid var(--border-color);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  flex-shrink: 0;
  position: relative;
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.status-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--success-color);
  animation: pulse 2s infinite;
}

.status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.update-icon {
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

/* 现代化快捷键提示 */
.shortcut-hint {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-heavy);
  z-index: 2500;
  animation: slideInRight var(--transition-medium) ease-out;
  min-width: 200px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.hint-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.hint-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: white;
  margin-bottom: var(--spacing-xs);
}

.hint-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.hint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-sm);
}

.hint-key {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: var(--border-radius-small);
  font-family: monospace;
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: white;
}

.hint-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-xs);
}

/* 现代化通知系统 */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 300px;
}

.notification {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-md);
  animation: slideInRight var(--transition-medium) ease-out;
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--success-color));
}

.notification.success::before {
  background: linear-gradient(to bottom, var(--success-color), #0f5132);
}

.notification.warning::before {
  background: linear-gradient(to bottom, var(--warning-color), #f57c00);
}

.notification.error::before {
  background: linear-gradient(to bottom, var(--danger-color), #b71c1c);
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.notification-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.notification-close {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.notification-close:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  body {
    width: 100vw;
    height: 100vh;
  }
  
  .toolbar {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-bar {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .stat-item {
    width: 100%;
    justify-content: center;
  }
  
  .window-groups {
    padding: var(--spacing-sm);
  }
  
  .modal-content {
    max-width: 95%;
  }
  
  .context-menu {
    max-width: 90%;
  }
  
  .shortcut-hint {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notifications-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}

/* 辅助动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
  border-radius: var(--border-radius-small);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--border-color), var(--secondary-color));
  border-radius: var(--border-radius-small);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--secondary-color), var(--text-secondary));
}

/* 选择文本样式 */
::selection {
  background: rgba(26, 115, 232, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(26, 115, 232, 0.3);
  color: var(--text-primary);
}

/* 焦点样式 */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 禁用状态 */
.disabled,
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 隐藏状态 */
.hidden {
  display: none !important;
}

/* 透明度工具类 */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 间距工具类 */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

/* 文本工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-bold { font-weight: 600; }
.text-normal { font-weight: 400; }

/* 颜色工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

/* 现代化加载动画 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 打印样式 */
@media print {
  .toolbar,
  .stats-bar,
  .batch-toolbar,
  .status-bar,
  .context-menu,
  .modal,
  .shortcut-hint,
  .notifications-container {
    display: none !important;
  }
  
  .content {
    overflow: visible !important;
  }
  
  .window-group {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .tab-item {
    background: white !important;
  }
}

.import-export-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-export-dialog .dialog-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.import-export-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.tab-btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: var(--primary-color);
  background-color: var(--background-color);
  border-bottom: 2px solid var(--primary-color);
}

.tab-btn:hover {
  background-color: var(--background-color);
}

.tab-content {
  display: none;
  padding: var(--spacing-md);
}

.tab-content.active {
  display: block;
}

.export-section,
.import-section {
  max-height: 400px;
  overflow-y: auto;
}

.export-section h4,
.import-section h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.format-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.format-option {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-option:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.format-option input[type="radio"] {
  margin-top: 2px;
}

.format-label {
  flex: 1;
}

.format-label strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.format-label small {
  color: var(--text-secondary);
  font-size: 12px;
}

.export-options,
.import-options {
  margin-top: var(--spacing-md);
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.file-upload-area {
  margin-bottom: var(--spacing-md);
}

.upload-zone {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: var(--primary-color);
  background-color: rgba(26, 115, 232, 0.1);
}

.upload-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-sm);
}

.upload-text {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.upload-hint {
  font-size: 12px;
  color: var(--text-secondary);
}

.import-preview {
  margin-top: var(--spacing-md);
}

.preview-content {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
}

.preview-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.preview-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
}

.preview-stat .stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.preview-stat .stat-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.preview-tabs h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
}

.preview-tab-item {
  padding: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.preview-tab-item:last-child {
  border-bottom: none;
}

.preview-tab-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.preview-tab-url {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-more {
  text-align: center;
  padding: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 12px;
  font-style: italic;
}

/* 导入导出状态指示器 */
.import-export-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  margin-top: var(--spacing-md);
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-size: 13px;
  color: var(--text-primary);
}

.status-progress {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-top: var(--spacing-xs);
}

.status-progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

/* 导入导出快捷键提示 */
.import-export-shortcuts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: 11px;
  z-index: 1001;
  display: none;
}

.import-export-shortcuts.show {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 导入导出结果通知 */
.import-export-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-md);
  z-index: 1002;
  max-width: 300px;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.notification-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.notification-icon {
  font-size: 16px;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.notification-message {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.notification-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

.notification-btn {
  padding: 2px 8px;
  font-size: 11px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.notification-btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.notification-btn.primary:hover {
  background-color: var(--primary-hover);
}

/* 标签页预览功能样式 */
.tab-preview-container {
  position: fixed;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  border-radius: var(--border-radius);
  display: none;
  animation: fadeIn 0.2s ease;
}

.tab-preview-container.visible {
  display: block;
}

.tab-preview-container.pinned {
  border: 2px solid var(--primary-color);
  box-shadow: 0 0 20px rgba(26, 115, 232, 0.3);
}

.tab-preview-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  width: 400px;
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.preview-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.preview-action-btn {
  padding: 4px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--border-radius-small);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.preview-action-btn:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

.preview-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.preview-screenshot {
  width: 200px;
  height: 150px;
  border-right: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
  flex-shrink: 0;
}

.screenshot-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.screenshot-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-align: center;
  height: 100%;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-xs);
}

.placeholder-text {
  font-size: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-xs);
}

.loading-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.preview-info {
  flex: 1;
  padding: var(--spacing-sm);
  overflow-y: auto;
}

.tab-basic-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.tab-favicon-large {
  width: 32px;
  height: 32px;
  position: relative;
  flex-shrink: 0;
}

.favicon-img {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-small);
}

.favicon-placeholder {
  display: none;
  width: 100%;
  height: 100%;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.tab-title-large {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  line-height: 1.3;
}

.tab-url-large {
  font-size: 11px;
  color: var(--text-secondary);
  word-break: break-all;
  line-height: 1.2;
}

.tab-metadata-section {
  margin-bottom: var(--spacing-sm);
}

.metadata-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: 12px;
}

.metadata-label {
  font-weight: 500;
  color: var(--text-secondary);
  width: 60px;
  flex-shrink: 0;
}

.metadata-value {
  color: var(--text-primary);
  flex: 1;
}

.status-value.active {
  color: var(--success-color);
}

.status-value.inactive {
  color: var(--text-secondary);
}

.tags-value {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.preview-tag {
  background-color: var(--surface-color);
  color: var(--text-primary);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
  border: 1px solid var(--border-color);
}

.tab-notes-section {
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.notes-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.notes-content {
  font-size: 12px;
  color: var(--text-primary);
  line-height: 1.4;
  max-height: 60px;
  overflow-y: auto;
}

.tab-stats-section {
  margin-bottom: var(--spacing-sm);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xs);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.stat-value {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.preview-footer {
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
  flex-shrink: 0;
}

.preview-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.preview-tab {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  background: none;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.preview-tab.active {
  color: var(--primary-color);
  background-color: var(--background-color);
  border-bottom: 2px solid var(--primary-color);
}

.preview-tab:hover {
  background-color: var(--background-color);
}

.preview-tab-content {
  display: none;
  padding: var(--spacing-sm);
  max-height: 120px;
  overflow-y: auto;
}

.preview-tab-content.active {
  display: block;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.quick-action-btn {
  padding: var(--spacing-xs);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  cursor: pointer;
  font-size: 11px;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.visit-history-list,
.related-tabs-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.history-time {
  font-size: 11px;
  color: var(--text-primary);
}

.history-duration {
  font-size: 10px;
  color: var(--text-secondary);
}

.related-tab-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-tab-item:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.related-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.related-info {
  flex: 1;
  min-width: 0;
}

.related-title {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-url {
  font-size: 10px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-history,
.empty-related {
  text-align: center;
  padding: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 12px;
  font-style: italic;
}

/* 预览容器响应式调整 */
@media (max-width: 500px) {
  .tab-preview-content {
    width: 350px;
  }
  
  .preview-body {
    flex-direction: column;
  }
  
  .preview-screenshot {
    width: 100%;
    height: 120px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}

/* 预览动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 预览加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 预览悬停效果 */
.tab-item:hover {
  background-color: var(--surface-color);
  cursor: pointer;
}

/* 窗口选择器对话框样式 */
.window-selector-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.window-selector-dialog .dialog-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  width: 500px;
  max-height: 70vh;
  overflow-y: auto;
}

.window-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 400px;
  overflow-y: auto;
}

.window-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  transition: all 0.2s ease;
}

.window-option:hover {
  background-color: var(--surface-color);
  border-color: var(--primary-color);
}

.window-option.new-window {
  border-style: dashed;
  border-color: var(--primary-color);
}

.window-info {
  flex: 1;
}

.window-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.window-details {
  font-size: 12px;
  color: var(--text-secondary);
}

.window-actions {
  margin-left: var(--spacing-md);
}

.move-to-window,
.move-to-new-window {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 12px;
  white-space: nowrap;
}

/* 全屏预览模式样式 */
.tab-preview-container.fullscreen-mode {
  backdrop-filter: blur(8px);
}

.tab-preview-container.fullscreen-mode .tab-preview-content {
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
}

.fullscreen-exit-hint {
  animation: fadeInHint 0.3s ease;
}

@keyframes fadeInHint {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hint-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.hint-shortcut {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 11px;
}

/* 预览模式按钮样式 */
.preview-action-btn[data-action="mode"] {
  font-size: 14px;
}

.preview-action-btn[data-action="mode"]:hover {
  background-color: rgba(26, 115, 232, 0.1);
}

/* 响应式调整 */
@media (max-width: 600px) {
  .window-selector-dialog .dialog-content {
    width: 90%;
    max-width: 400px;
  }
  
  .window-option {
    flex-direction: column;
    align-items: stretch;
  }
  
  .window-actions {
    margin-left: 0;
    margin-top: var(--spacing-sm);
  }
}

/* 批量操作功能样式 */
.batch-operations-btn {
  margin-left: var(--spacing-sm);
}

.batch-operations-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-operations-panel .panel-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.batch-operations-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.batch-operations-panel .panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary);
}

.batch-operations-panel .panel-body {
  flex: 1;
  overflow-y: auto;
}

.batch-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.batch-tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.batch-tab.active {
  color: var(--primary-color);
  background-color: var(--background-color);
  border-bottom-color: var(--primary-color);
}

.batch-tab:hover {
  background-color: var(--background-color);
}

.batch-operations-panel .tab-content {
  display: none;
  padding: var(--spacing-md);
}

.batch-operations-panel .tab-content.active {
  display: block;
}

/* 选择区域样式 */
.selection-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.selection-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.selection-group {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
}

.selection-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.condition-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.condition-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  color: var(--text-primary);
}

.condition-value {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  color: var(--text-primary);
}

.quick-selection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.window-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.window-checkbox {
  margin-right: var(--spacing-xs);
}

.selection-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  font-size: 14px;
  color: var(--text-primary);
}

/* 操作区域样式 */
.operations-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.operation-category {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
}

.operation-category h5 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.operation-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.operation-btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.operation-btn .icon {
  font-size: 16px;
}

/* 预设区域样式 */
.presets-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.presets-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.preset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--surface-color);
}

.preset-info {
  flex: 1;
}

.preset-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.preset-description {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: var(--spacing-xs);
}

.preset-operations {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.operation-tag {
  background-color: var(--primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-small);
  font-size: 11px;
}

.preset-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.empty-presets {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  font-style: italic;
}

/* 历史区域样式 */
.history-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--surface-color);
}

.history-info {
  flex: 1;
}

.history-operation {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.history-time {
  color: var(--text-secondary);
  font-size: 12px;
  margin-bottom: var(--spacing-xs);
}

.history-details {
  color: var(--text-secondary);
  font-size: 13px;
}

.history-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.empty-history {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  font-style: italic;
}

/* 面板底部样式 */
.batch-operations-panel .panel-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 900px) {
  .batch-operations-panel .panel-content {
    width: 95%;
    max-width: 700px;
  }
  
  .operations-grid {
    grid-template-columns: 1fr;
  }
  
  .condition-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quick-selection {
    flex-direction: column;
  }
}

/* 统计分析面板样式 */
.statistics-btn {
  margin-left: var(--spacing-sm);
}

.statistics-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statistics-panel .panel-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.statistics-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.statistics-panel .panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary);
}

.statistics-panel .panel-body {
  flex: 1;
  overflow-y: auto;
}

.statistics-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
  overflow-x: auto;
}

.stats-tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  min-width: 100px;
}

.stats-tab.active {
  color: var(--primary-color);
  background-color: var(--background-color);
  border-bottom-color: var(--primary-color);
}

.stats-tab:hover {
  background-color: var(--background-color);
}

.statistics-panel .tab-content {
  display: none;
  padding: var(--spacing-md);
}

.statistics-panel .tab-content.active {
  display: block;
}

/* 概览统计样式 */
.statistics-overview h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.stat-icon {
  font-size: 24px;
  margin-right: var(--spacing-md);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 快速洞察样式 */
.quick-insights h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.insight-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-small);
  border-left: 4px solid;
}

.insight-item.warning {
  background-color: #fff8e1;
  border-left-color: var(--warning-color);
}

.insight-item.info {
  background-color: #e8f0fe;
  border-left-color: var(--primary-color);
}

.insight-item.tip {
  background-color: #e8f5e8;
  border-left-color: var(--success-color);
}

.insight-icon {
  font-size: 16px;
  margin-right: var(--spacing-sm);
}

.insight-text {
  font-size: 13px;
  color: var(--text-primary);
  line-height: 1.4;
}

/* 使用统计样式 */
.usage-statistics h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.usage-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-container {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.chart-container h5 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 14px;
}

.chart-area {
  height: 200px;
  position: relative;
}

/* 简单图表样式 */
.simple-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 150px;
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.chart-bar {
  flex: 1;
  margin: 0 2px;
  background-color: var(--primary-color);
  border-radius: 2px 2px 0 0;
  position: relative;
  min-height: 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: var(--text-secondary);
}

.bar-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: var(--text-secondary);
  text-align: center;
}

.chart-legend {
  display: flex;
  justify-content: center;
  padding: var(--spacing-sm);
  gap: var(--spacing-md);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* 生命周期图表样式 */
.lifecycle-chart {
  display: flex;
  gap: var(--spacing-md);
  height: 100%;
}

.lifecycle-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.lifecycle-stat {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.lifecycle-stat .stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.lifecycle-stat .stat-label {
  font-size: 11px;
  color: var(--text-secondary);
}

.lifecycle-pie {
  width: 120px;
  height: 120px;
  position: relative;
  margin: auto;
}

.pie-chart {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%);
  transform-origin: center;
}

.pie-segment.short {
  background-color: var(--primary-color);
}

.pie-segment.medium {
  background-color: var(--warning-color);
}

.pie-segment.long {
  background-color: var(--success-color);
}

/* 使用表格样式 */
.usage-details h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.usage-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  overflow: hidden;
}

.usage-table th,
.usage-table td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.usage-table th {
  background-color: var(--background-color);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 13px;
}

.usage-table td {
  color: var(--text-secondary);
  font-size: 12px;
}

/* 域名统计样式 */
.domain-statistics h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.domain-summary {
  margin-bottom: var(--spacing-lg);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.summary-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.summary-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.summary-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* 域名图表样式 */
.domain-chart h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.domain-chart {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
}

.chart-bars.horizontal {
  flex-direction: column;
  height: auto;
  gap: var(--spacing-xs);
}

.chart-bar-horizontal {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.bar-label {
  flex: 0 0 120px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: right;
}

.bar-container {
  flex: 1;
  position: relative;
  height: 20px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.bar-value {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: var(--text-primary);
  font-weight: 500;
}

/* 域名表格样式 */
.domain-list h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.domain-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  overflow: hidden;
}

.domain-table th,
.domain-table td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.domain-table th {
  background-color: var(--background-color);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 13px;
}

.domain-table td {
  color: var(--text-secondary);
  font-size: 12px;
}

.domain-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.domain-favicon {
  border-radius: 2px;
}

/* 分类统计样式 */
.category-statistics h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.category-overview {
  margin-bottom: var(--spacing-lg);
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.category-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.category-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* 分类图表样式 */
.category-chart h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.category-chart {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
}

.category-pie-chart {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.pie-chart-container {
  flex: 0 0 200px;
  height: 200px;
  position: relative;
}

.pie-chart.large {
  width: 200px;
  height: 200px;
}

.pie-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.pie-legend .legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.pie-legend .legend-color {
  width: 14px;
  height: 14px;
  border-radius: 2px;
}

.pie-legend .legend-label {
  flex: 1;
  font-size: 13px;
  color: var(--text-primary);
}

.pie-legend .legend-value {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 标签云样式 */
.tags-analysis h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.tags-cloud {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  text-align: center;
  line-height: 1.8;
}

.tag-item {
  display: inline-block;
  margin: 2px 4px;
  padding: 2px 8px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background-color: var(--primary-hover);
  transform: scale(1.05);
}

/* 性能分析样式 */
.performance-statistics h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.performance-overview {
  margin-bottom: var(--spacing-lg);
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.performance-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.perf-icon {
  font-size: 24px;
  margin-right: var(--spacing-md);
}

.perf-info {
  flex: 1;
}

.perf-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.perf-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 内存和加载时间分析样式 */
.memory-analysis h5,
.load-time-analysis h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.memory-chart,
.load-time-chart {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
}

.memory-chart .chart-area {
  height: 200px;
  position: relative;
  margin-bottom: var(--spacing-md);
}

.line-chart {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  overflow: hidden;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-xs);
}

.chart-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.memory-stats {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-md);
}

.memory-stat {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.memory-stat .stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.memory-stat .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 加载时间直方图样式 */
.chart-histogram {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 150px;
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-md);
}

.histogram-bar {
  flex: 1;
  margin: 0 2px;
  background-color: var(--success-color);
  border-radius: 2px 2px 0 0;
  position: relative;
  min-height: 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.bar-count {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: var(--text-secondary);
}

.bar-range {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 9px;
  color: var(--text-secondary);
  text-align: center;
  width: 60px;
}

.load-time-summary {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-md);
}

.summary-item {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.summary-item .summary-label {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.summary-item .summary-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 趋势分析样式 */
.trends-statistics h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 16px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.time-range-selector label {
  font-size: 14px;
  color: var(--text-primary);
}

.time-range-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--background-color);
  color: var(--text-primary);
}

.trends-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.trends-chart {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.trends-chart .chart-container {
  height: 200px;
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.trends-info {
  display: flex;
  justify-content: center;
}

.trend-stat {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.trend-change {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.trend-change.positive {
  color: var(--success-color);
}

.trend-change.negative {
  color: var(--danger-color);
}

.trend-label {
  font-size: 11px;
  color: var(--text-secondary);
}

/* 模式趋势样式 */
.pattern-trends-chart {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-sm);
}

.pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.pattern-name {
  font-size: 12px;
  color: var(--text-primary);
}

.pattern-trend {
  display: flex;
  align-items: center;
  gap: 2px;
}

.trend-arrow {
  font-size: 14px;
}

.pattern-trend.up .trend-arrow {
  color: var(--success-color);
}

.pattern-trend.down .trend-arrow {
  color: var(--danger-color);
}

.pattern-trend.stable .trend-arrow {
  color: var(--text-secondary);
}

.trend-value {
  font-size: 11px;
  font-weight: 500;
}

/* 预测分析样式 */
.predictions h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 14px;
}

.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.prediction-card {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.prediction-icon {
  font-size: 24px;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.prediction-content {
  flex: 1;
}

.prediction-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.prediction-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: var(--spacing-xs);
}

.prediction-confidence {
  font-size: 11px;
  color: var(--text-muted);
  font-style: italic;
}

/* 面板底部样式 */
.statistics-panel .panel-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 1100px) {
  .statistics-panel .panel-content {
    width: 95%;
  }
  
  .usage-charts,
  .trends-charts {
    grid-template-columns: 1fr;
  }
  
  .overview-grid,
  .summary-grid,
  .performance-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .category-pie-chart {
    flex-direction: column;
    text-align: center;
  }
  
  .pie-chart-container {
    margin-bottom: var(--spacing-md);
  }
}

@media (max-width: 800px) {
  .statistics-tabs {
    flex-wrap: wrap;
  }
  
  .stats-tab {
    flex: 1 1 auto;
    min-width: 80px;
  }
  
  .chart-bars {
    flex-direction: column;
    height: auto;
    gap: var(--spacing-xs);
  }
  
  .chart-bar {
    width: 100%;
    height: 20px;
    margin: 0;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .predictions-grid {
    grid-template-columns: 1fr;
  }
}

/* 帮助系统样式 */
.help-button {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  margin-left: 8px;
  transition: all 0.2s ease;
}

.help-button:hover {
  background: var(--surface-hover);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 欢迎引导样式 */
.welcome-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.welcome-guide {
  background: var(--background-color);
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.guide-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.guide-header h2 {
  margin: 0 0 8px;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.guide-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.guide-content {
  padding: 24px;
  min-height: 300px;
}

.guide-step {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.guide-step.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
}

.step-content h3 {
  margin: 0 0 16px;
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
}

.step-content ul {
  margin: 0;
  padding-left: 20px;
}

.step-content li {
  margin-bottom: 12px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.step-content kbd {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 12px;
  margin: 0 2px;
}

.guide-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.guide-indicators {
  display: flex;
  gap: 8px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.indicator:hover {
  background: var(--primary-hover);
}

.guide-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.guide-btn:hover {
  background: var(--primary-hover);
}

.guide-btn:disabled {
  background: var(--surface-color);
  color: var(--text-muted);
  cursor: not-allowed;
}

.guide-prev {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.guide-prev:hover:not(:disabled) {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.guide-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
}

.guide-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  cursor: pointer;
}

.guide-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.guide-close {
  background: var(--success-color);
  color: white;
}

.guide-close:hover {
  background: #0d5016;
}

/* 帮助面板样式 */
.help-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.help-content {
  position: relative;
  background: var(--background-color);
  border-radius: 12px;
  max-width: 800px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.help-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
}

.help-close {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.help-close:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.help-body {
  display: flex;
  flex-direction: column;
  height: 60vh;
  overflow: hidden;
}

.help-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.help-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 16px 20px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.help-tab:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.help-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--background-color);
}

.help-tab-content {
  display: none;
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.help-tab-content.active {
  display: block;
}

.help-tab-content h4 {
  margin: 0 0 20px;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.help-tab-content h5 {
  margin: 0 0 8px;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.help-section {
  margin-bottom: 24px;
}

.help-section p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: var(--surface-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.shortcut-key {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.shortcut-key kbd {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-desc {
  color: var(--text-secondary);
  font-size: 14px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: var(--surface-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: var(--surface-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: 10px;
  flex-shrink: 0;
}

.feature-info h5 {
  margin: 0 0 8px;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.feature-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-item {
  padding: 20px;
  background: var(--surface-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.faq-item h5 {
  margin: 0 0 12px;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.faq-item p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.help-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background: var(--surface-color);
}

.help-footer .btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.help-footer .btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
}

.help-footer .btn-primary:hover {
  background: var(--primary-hover);
}

.help-footer .btn-secondary {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.help-footer .btn-secondary:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

/* 错误和成功通知样式 */
.error-notification,
.success-notification {
  position: fixed;
  top: 10px;
  left: 10px;
  right: 10px;
  z-index: 10001;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-content,
.success-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-size: 14px;
  line-height: 1.4;
}

.error-content {
  background: #fef7f0;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.success-content {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.error-icon,
.success-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.error-message,
.success-message {
  flex: 1;
  font-weight: 500;
}

.error-retry-btn,
.error-close-btn,
.success-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.error-retry-btn {
  background: var(--danger-color);
  color: white;
}

.error-retry-btn:hover {
  background: #c82333;
}

.error-close-btn,
.success-close-btn {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1;
}

.error-close-btn:hover,
.success-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}
