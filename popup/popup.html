<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome标签页管理器</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="app-container">
    <!-- 头部工具栏 -->
    <header class="toolbar">
      <div class="toolbar-left">
        <div class="app-logo">
          <div class="logo-icon">🗂️</div>
          <div class="logo-text">
            <h1 class="app-title">TabManager</h1>
            <p class="app-subtitle">智能标签页管理</p>
          </div>
        </div>
        <button id="refreshBtn" class="btn btn-icon btn-refresh" title="刷新数据">
          <span class="icon">🔄</span>
        </button>
      </div>
      <div class="toolbar-right">
        <div class="search-container">
          <div class="search-input-wrapper">
            <span class="search-icon">🔍</span>
            <input type="text" id="searchInput" placeholder="搜索标签页..." class="search-input">
            <button id="searchBtn" class="btn btn-icon search-btn">
              <span class="icon">⚡</span>
            </button>
          </div>
        </div>
        <button id="settingsBtn" class="btn btn-icon" title="设置">
          <span class="icon">⚙️</span>
        </button>
      </div>
    </header>

    <!-- 统计信息 -->
    <div class="stats-bar">
      <div class="stat-item">
        <div class="stat-icon">🪟</div>
        <div class="stat-content">
          <span class="stat-label">窗口</span>
          <span id="windowCount" class="stat-value">0</span>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">📄</div>
        <div class="stat-content">
          <span class="stat-label">标签页</span>
          <span id="tabCount" class="stat-value">0</span>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">💾</div>
        <div class="stat-content">
          <span class="stat-label">内存节省</span>
          <span id="memorySaved" class="stat-value">0%</span>
        </div>
      </div>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="batch-toolbar" id="batchToolbar" style="display: none;">
      <div class="batch-info">
        <span class="batch-icon">✅</span>
        已选择 <span id="selectedCount">0</span> 个标签页
      </div>
      <div class="batch-actions">
        <button id="batchOpenNewWindow" class="btn btn-small btn-primary">
          <span class="icon">🔗</span>
          新窗口打开
        </button>
        <button id="batchOpenCurrentWindow" class="btn btn-small btn-secondary">
          <span class="icon">📄</span>
          当前窗口打开
        </button>
        <button id="batchClose" class="btn btn-small btn-danger">
          <span class="icon">✕</span>
          关闭选中
        </button>
        <button id="clearSelection" class="btn btn-small btn-ghost">
          <span class="icon">🚫</span>
          取消选择
        </button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <main class="content">
      <div id="loadingIndicator" class="loading-indicator">
        <div class="loading-animation">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
        </div>
        <div class="loading-text">正在加载标签页数据...</div>
      </div>

      <div id="emptyState" class="empty-state" style="display: none;">
        <div class="empty-animation">
          <div class="empty-icon">📭</div>
          <div class="empty-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
          </div>
        </div>
        <div class="empty-content">
          <div class="empty-title">没有找到标签页</div>
          <div class="empty-subtitle">尝试调整搜索条件或刷新数据</div>
          <button id="emptyStateRefreshBtn" class="btn btn-primary empty-action">
            <span class="icon">🔄</span>
            刷新数据
          </button>
        </div>
      </div>

      <div id="windowGroups" class="window-groups">
        <!-- 窗口组将在这里动态生成 -->
      </div>
    </main>

    <!-- 底部状态栏 -->
    <footer class="status-bar">
      <div class="status-left">
        <span class="status-indicator"></span>
        <span id="statusText">就绪</span>
      </div>
      <div class="status-right">
        <span class="update-icon">🕐</span>
        <span id="lastUpdate">未更新</span>
      </div>
    </footer>
  </div>

  <!-- 右键菜单 -->
  <div id="contextMenu" class="context-menu modern-menu" style="display: none;">
    <div class="menu-item" data-action="edit-tab">
      <span class="menu-icon">✏️</span>
      <span class="menu-text">编辑标签页</span>
      <span class="menu-shortcut">Ctrl+E</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item" data-action="open-new-window">
      <span class="menu-icon">🔗</span>
      <span class="menu-text">在新窗口打开</span>
      <span class="menu-shortcut">Ctrl+N</span>
    </div>
    <div class="menu-item" data-action="open-current-window">
      <span class="menu-icon">📄</span>
      <span class="menu-text">在当前窗口打开</span>
      <span class="menu-shortcut">Enter</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item" data-action="copy-url">
      <span class="menu-icon">📋</span>
      <span class="menu-text">复制URL</span>
      <span class="menu-shortcut">Ctrl+C</span>
    </div>
    <div class="menu-item" data-action="copy-title">
      <span class="menu-icon">📝</span>
      <span class="menu-text">复制标题</span>
      <span class="menu-shortcut">Ctrl+Shift+C</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item menu-item-danger" data-action="close-tab">
      <span class="menu-icon">🗑️</span>
      <span class="menu-text">关闭标签页</span>
      <span class="menu-shortcut">Del</span>
    </div>
  </div>

  <!-- 模态框 -->
  <div id="modal" class="modal modern-modal" style="display: none;">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitle">标题</h3>
        <button id="modalClose" class="btn btn-icon modal-close">
          <span class="icon">✕</span>
        </button>
      </div>
      <div class="modal-body" id="modalBody">
        <!-- 模态框内容 -->
      </div>
      <div class="modal-footer">
        <button id="modalCancel" class="btn btn-secondary">
          <span class="icon">🚫</span>
          取消
        </button>
        <button id="modalConfirm" class="btn btn-primary">
          <span class="icon">✅</span>
          确认
        </button>
      </div>
    </div>
  </div>

  <!-- 快捷键提示 -->
  <div id="shortcutHint" class="shortcut-hint" style="display: none;">
    <div class="hint-content">
      <div class="hint-title">快捷键</div>
      <div class="hint-items">
        <div class="hint-item">
          <span class="hint-key">Ctrl + F</span>
          <span class="hint-desc">搜索</span>
        </div>
        <div class="hint-item">
          <span class="hint-key">F5</span>
          <span class="hint-desc">刷新</span>
        </div>
        <div class="hint-item">
          <span class="hint-key">Ctrl + A</span>
          <span class="hint-desc">全选</span>
        </div>
        <div class="hint-item">
          <span class="hint-key">Esc</span>
          <span class="hint-desc">取消</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 通知系统 -->
  <div id="notifications" class="notifications-container"></div>

  <script src="../utils/chromeAPI.js"></script>
  <script src="../utils/storageManager.js"></script>
  <script src="popup-bundle.js"></script>
</body>
</html>