/**
 * Chrome标签页管理器 - 弹窗页面
 * 非模块化版本，用于Chrome扩展弹窗
 */

// 全局变量
let windowGroups = [];
let selectedTabs = new Set();
let searchQuery = '';
let collapsedGroups = new Set();
let isLoading = false;
let userSettings = {};
let isFirstTime = false; // 标记是否首次使用
let eventListeners = []; // 存储事件监听器以便清理

// DOM元素引用
const elements = {
  // 工具栏元素
  refreshBtn: null,
  searchInput: null,
  searchBtn: null,
  settingsBtn: null,
  
  // 统计信息
  windowCount: null,
  tabCount: null,
  memorySaved: null,
  
  // 批量操作
  batchToolbar: null,
  selectedCount: null,
  batchOpenNewWindow: null,
  batchOpenCurrentWindow: null,
  batchClose: null,
  clearSelection: null,
  
  // 主内容
  loadingIndicator: null,
  emptyState: null,
  windowGroups: null,
  
  // 状态栏
  statusText: null,
  lastUpdate: null,
  
  // 右键菜单和模态框
  contextMenu: null,
  modal: null,
  modalTitle: null,
  modalBody: null,
  modalClose: null,
  modalCancel: null,
  modalConfirm: null
};

/**
 * 初始化应用
 */
async function initApp() {
  try {
    console.log('TabManager: 开始初始化...');
    
    // 添加延迟以确保Chrome扩展环境准备就绪
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 检查Chrome扩展环境
    if (!chrome || !chrome.tabs) {
      console.error('TabManager: Chrome扩展API不可用');
      showError('Chrome扩展API不可用，请确保在正确的Chrome扩展环境中运行');
      return;
    }
    
    bindElements();
    
    // 检查DOM元素是否正确绑定
    const requiredElements = ['loadingIndicator', 'windowGroups', 'emptyState'];
    for (const elementName of requiredElements) {
      if (!elements[elementName]) {
        console.error(`TabManager: 必需的DOM元素 ${elementName} 未找到`);
        showError(`页面元素加载失败，请刷新页面重试`);
        return;
      }
    }
    
    bindEvents();
    setStatus('正在初始化...');
    
    // 显示加载状态
    showLoading(true);
    
    // 确保界面基本结构正确显示
    const appContainer = document.querySelector('.app-container');
    if (appContainer) {
      appContainer.style.display = 'flex';
      appContainer.style.height = '100%';
      appContainer.style.minHeight = '600px';
    }
    
    await loadUserSettings();
    await loadCollapsedGroups();
    await checkFirstTimeUser(); // 检查首次使用
    
    console.log('TabManager: 开始刷新数据...');
    await refreshData();
    
    addHelpButton(); // 添加帮助按钮
    showFeatureTips(); // 显示功能提示
    
    setStatus('就绪');
    console.log('TabManager: 弹窗初始化完成');
  } catch (error) {
    console.error('TabManager: 初始化失败:', error);
    setStatus('初始化失败');
    
    // 隐藏加载状态，显示错误信息
    showLoading(false);
    showError('应用初始化失败，请刷新页面重试', true);
    
    // 显示基本的错误恢复界面
    showErrorRecoveryUI();
  }
}

/**
 * 绑定DOM元素引用
 */
function bindElements() {
  elements.refreshBtn = document.getElementById('refreshBtn');
  elements.searchInput = document.getElementById('searchInput');
  elements.searchBtn = document.getElementById('searchBtn');
  elements.settingsBtn = document.getElementById('settingsBtn');
  
  elements.windowCount = document.getElementById('windowCount');
  elements.tabCount = document.getElementById('tabCount');
  elements.memorySaved = document.getElementById('memorySaved');
  
  elements.batchToolbar = document.getElementById('batchToolbar');
  elements.selectedCount = document.getElementById('selectedCount');
  elements.batchOpenNewWindow = document.getElementById('batchOpenNewWindow');
  elements.batchOpenCurrentWindow = document.getElementById('batchOpenCurrentWindow');
  elements.batchClose = document.getElementById('batchClose');
  elements.clearSelection = document.getElementById('clearSelection');
  
  elements.loadingIndicator = document.getElementById('loadingIndicator');
  elements.emptyState = document.getElementById('emptyState');
  elements.windowGroups = document.getElementById('windowGroups');
  
  elements.statusText = document.getElementById('statusText');
  elements.lastUpdate = document.getElementById('lastUpdate');
  
  elements.contextMenu = document.getElementById('contextMenu');
  elements.modal = document.getElementById('modal');
  elements.modalTitle = document.getElementById('modalTitle');
  elements.modalBody = document.getElementById('modalBody');
  elements.modalClose = document.getElementById('modalClose');
  elements.modalCancel = document.getElementById('modalCancel');
  elements.modalConfirm = document.getElementById('modalConfirm');
}

/**
 * 添加事件监听器并记录以便清理
 */
function addEventListenerWithCleanup(element, event, handler, options = false) {
  element.addEventListener(event, handler, options);
  eventListeners.push({ element, event, handler, options });
}

/**
 * 清理所有事件监听器
 */
function cleanupEventListeners() {
  eventListeners.forEach(({ element, event, handler, options }) => {
    try {
      element.removeEventListener(event, handler, options);
    } catch (error) {
      console.warn('清理事件监听器失败:', error);
    }
  });
  eventListeners = [];
}

/**
 * 绑定事件监听器
 */
function bindEvents() {
  // 工具栏事件
  addEventListenerWithCleanup(elements.refreshBtn, 'click', () => refreshData());
  addEventListenerWithCleanup(elements.searchInput, 'input', (e) => handleSearch(e.target.value));
  addEventListenerWithCleanup(elements.searchBtn, 'click', () => focusSearch());
  addEventListenerWithCleanup(elements.settingsBtn, 'click', () => openSettings());
  
  // 空状态刷新按钮
  const emptyStateRefreshBtn = document.getElementById('emptyStateRefreshBtn');
  if (emptyStateRefreshBtn) {
    addEventListenerWithCleanup(emptyStateRefreshBtn, 'click', () => refreshData());
  }
  
  // 批量操作事件
  addEventListenerWithCleanup(elements.batchOpenNewWindow, 'click', () => batchOpenInNewWindow());
  addEventListenerWithCleanup(elements.batchOpenCurrentWindow, 'click', () => batchOpenInCurrentWindow());
  addEventListenerWithCleanup(elements.batchClose, 'click', () => batchCloseTabs());
  addEventListenerWithCleanup(elements.clearSelection, 'click', () => clearSelection());
  
  // 模态框事件
  addEventListenerWithCleanup(elements.modalClose, 'click', () => closeModal());
  addEventListenerWithCleanup(elements.modalCancel, 'click', () => closeModal());
  
  // 键盘快捷键
  addEventListenerWithCleanup(document, 'keydown', (e) => handleKeyboard(e));
  
  // 右键菜单
  addEventListenerWithCleanup(document, 'contextmenu', (e) => handleContextMenu(e));
  addEventListenerWithCleanup(document, 'click', () => hideContextMenu());
  
  // 搜索快捷键
  addEventListenerWithCleanup(elements.searchInput, 'keydown', (e) => {
    if (e.key === 'Escape') {
      elements.searchInput.value = '';
      handleSearch('');
    }
  });
  
  // 页面卸载时清理
  addEventListenerWithCleanup(window, 'beforeunload', cleanupEventListeners);
}

/**
 * 刷新数据
 */
async function refreshData() {
  if (isLoading) return;
  
  try {
    isLoading = true;
    showLoading(true);
    setStatus('正在收集标签页数据...');
    
    console.log('TabManager: 开始收集标签页数据...');
    
    // 检查Chrome API可用性
    if (!chrome || !chrome.windows || !chrome.tabs) {
      throw new Error('Chrome API不可用');
    }
    
    // 添加超时处理的Chrome API调用
    const getAllWindowsWithTimeout = () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Chrome API调用超时'));
        }, 10000); // 10秒超时
        
        chrome.windows.getAll({
          populate: true,
          windowTypes: ['normal']
        }, (windows) => {
          clearTimeout(timeout);
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(windows || []);
          }
        });
      });
    };
    
    // 获取所有窗口和标签页
    const allWindows = await getAllWindowsWithTimeout();
    
    console.log(`TabManager: 获取到 ${allWindows.length} 个窗口`);
    
    // 转换为窗口组格式
    windowGroups = allWindows.map(window => ({
      id: window.id,
      title: `窗口 ${window.id}`,
      customName: null,
      focused: window.focused,
      incognito: window.incognito,
      tabs: window.tabs.map(tab => ({
        id: tab.id,
        title: tab.title,
        url: tab.url,
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }))
    }));
    
    console.log(`TabManager: 转换为 ${windowGroups.length} 个窗口组`);
    
    // 应用搜索过滤
    if (searchQuery) {
      windowGroups = filterWindowGroups(windowGroups, searchQuery);
      console.log(`TabManager: 搜索过滤后剩余 ${windowGroups.length} 个窗口组`);
    }
    
    // 应用折叠状态
    applyCollapsedStates();
    
    // 渲染界面
    renderWindowGroups();
    updateStats();
    updateLastUpdateTime();
    
    setStatus('数据更新完成');
    console.log('TabManager: 数据刷新完成');
    
  } catch (error) {
    console.error('TabManager: 刷新数据失败:', error);
    setStatus('数据更新失败');
    
    // 显示用户友好的错误信息
    let errorMessage = '无法获取标签页数据';
    if (error.message.includes('permission')) {
      errorMessage = '权限不足，请检查扩展权限设置';
    } else if (error.message.includes('network')) {
      errorMessage = '网络连接问题，请检查网络连接';
    } else if (error.message.includes('Chrome API')) {
      errorMessage = 'Chrome扩展API不可用，请确保在正确的扩展环境中运行';
    } else if (error.message.includes('超时')) {
      errorMessage = 'Chrome API调用超时，请稍后重试';
    }
    
    showError(errorMessage, true);
    
    // 显示空状态或错误恢复界面
    showEmptyStateOrError();
    
  } finally {
    isLoading = false;
    showLoading(false);
    
    // 强制确保界面正确显示
    setTimeout(() => {
      const loadingIndicator = document.getElementById('loadingIndicator');
      const windowGroups = document.getElementById('windowGroups');
      const emptyState = document.getElementById('emptyState');
      
      if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
      }
      
      // 如果没有窗口组数据，显示空状态
      if (windowGroups && (!window.windowGroups || window.windowGroups.length === 0)) {
        windowGroups.style.display = 'none';
        if (emptyState) {
          emptyState.style.display = 'flex';
        }
      } else if (windowGroups) {
        windowGroups.style.display = 'block';
        if (emptyState) {
          emptyState.style.display = 'none';
        }
      }
    }, 100);
  }
}

/**
 * 显示空状态或错误恢复界面
 */
function showEmptyStateOrError() {
  try {
    const emptyState = elements.emptyState || document.getElementById('emptyState');
    const windowGroups = elements.windowGroups || document.getElementById('windowGroups');
    const loadingIndicator = elements.loadingIndicator || document.getElementById('loadingIndicator');
    
    // 确保加载指示器被隐藏
    if (loadingIndicator) {
      loadingIndicator.style.display = 'none';
    }
    
    if (emptyState) {
      emptyState.style.display = 'flex';
    }
    if (windowGroups) {
      windowGroups.style.display = 'none';
    }
    
    console.log('TabManager: 显示空状态界面');
  } catch (error) {
    console.error('显示空状态失败:', error);
    // 作为最后的备选，显示一个基本的错误界面
    showBasicErrorInterface();
  }
}

/**
 * 显示基本错误界面（作为最后的备选）
 */
function showBasicErrorInterface() {
  try {
    const appContainer = document.querySelector('.app-container');
    if (!appContainer) return;
    
    appContainer.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 20px;
        text-align: center;
        background-color: #f8f9fa;
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
        <h2 style="margin: 0 0 8px 0; color: #202124; font-size: 18px;">加载失败</h2>
        <p style="margin: 0 0 16px 0; color: #5f6368; font-size: 14px;">
          扩展加载过程中遇到问题
        </p>
        <button onclick="window.location.reload()" style="
          background-color: #1a73e8;
          color: white;
          border: none;
          border-radius: 6px;
          padding: 10px 20px;
          cursor: pointer;
          font-size: 14px;
        ">🔄 重新加载</button>
      </div>
    `;
    
    console.log('TabManager: 显示基本错误界面');
  } catch (error) {
    console.error('显示基本错误界面失败:', error);
  }
}

/**
 * 显示错误恢复界面
 */
function showErrorRecoveryUI() {
  try {
    const windowGroups = document.getElementById('windowGroups');
    if (windowGroups) {
      windowGroups.innerHTML = `
        <div class="error-recovery-container" style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          padding: 20px;
          text-align: center;
          color: #5f6368;
        ">
          <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
          <h3 style="margin: 0 0 10px 0; color: #202124;">初始化失败</h3>
          <p style="margin: 0 0 20px 0; line-height: 1.4;">
            扩展初始化过程中遇到问题，请尝试以下解决方案：
          </p>
          <div style="text-align: left; margin: 20px 0;">
            <p style="margin: 5px 0;"><strong>1.</strong> 点击刷新按钮重新加载</p>
            <p style="margin: 5px 0;"><strong>2.</strong> 检查Chrome扩展权限设置</p>
            <p style="margin: 5px 0;"><strong>3.</strong> 重新启动Chrome浏览器</p>
            <p style="margin: 5px 0;"><strong>4.</strong> 重新安装扩展</p>
          </div>
          <button id="errorRecoveryReloadBtn" style="
            padding: 10px 20px;
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
          ">🔄 重新加载</button>
        </div>
      `;
      
      // 添加事件监听器
      const reloadBtn = windowGroups.querySelector('#errorRecoveryReloadBtn');
      if (reloadBtn) {
        reloadBtn.addEventListener('click', () => {
          window.location.reload();
        });
      }
      
      windowGroups.style.display = 'block';
    }
  } catch (error) {
    console.error('显示错误恢复界面失败:', error);
  }
}

/**
 * 显示/隐藏加载状态
 */
function showLoading(show) {
  try {
    const loadingIndicator = elements.loadingIndicator || document.getElementById('loadingIndicator');
    const windowGroups = elements.windowGroups || document.getElementById('windowGroups');
    const emptyState = elements.emptyState || document.getElementById('emptyState');
    
    if (loadingIndicator) {
      loadingIndicator.style.display = show ? 'flex' : 'none';
    }
    if (windowGroups) {
      windowGroups.style.display = show ? 'none' : 'block';
    }
    if (emptyState) {
      emptyState.style.display = 'none';
    }
    
    console.log(`TabManager: ${show ? '显示' : '隐藏'}加载状态`);
  } catch (error) {
    console.error('切换加载状态失败:', error);
  }
}

/**
 * 渲染窗口组（优化版本）
 */
function renderWindowGroups() {
  const container = elements.windowGroups;
  
  // 使用文档片段提高性能
  const fragment = document.createDocumentFragment();

  if (windowGroups.length === 0) {
    elements.emptyState.style.display = 'flex';
    elements.windowGroups.style.display = 'none';
    return;
  }

  elements.emptyState.style.display = 'none';
  elements.windowGroups.style.display = 'block';

  // 批量创建元素，避免频繁DOM操作
  const elementsToCreate = [];
  windowGroups.forEach((group, index) => {
    elementsToCreate.push(createWindowGroupElement(group, index));
  });

  // 批量添加到文档片段
  elementsToCreate.forEach(element => {
    fragment.appendChild(element);
  });

  // 清空容器并一次性添加所有元素
  container.innerHTML = '';
  container.appendChild(fragment);
}

/**
 * 创建窗口组元素
 */
function createWindowGroupElement(group, index) {
  const groupDiv = document.createElement('div');
  groupDiv.className = `window-group ${group.focused ? 'focused' : ''}`;
  groupDiv.dataset.windowId = group.id;

  const isCollapsed = collapsedGroups.has(group.id);
  
  groupDiv.innerHTML = `
    <div class="window-group-header" data-window-id="${group.id}">
      <div class="window-group-info">
        <span class="collapse-icon ${isCollapsed ? 'collapsed' : ''}">▼</span>
        <span class="window-group-title">${group.customName || group.title}</span>
        <span class="window-group-count">${group.tabs.length}</span>
        ${group.incognito ? '<span class="incognito-badge">🕶️</span>' : ''}
      </div>
      <div class="window-group-actions">
        <button class="btn btn-icon btn-small" title="重命名窗口组" data-action="rename">
          <span class="icon">✏️</span>
        </button>
        <button class="btn btn-icon btn-small" title="关闭整个窗口" data-action="close-window">
          <span class="icon">✕</span>
        </button>
      </div>
    </div>
    <div class="tab-list ${isCollapsed ? 'collapsed' : ''}" data-window-id="${group.id}">
      ${group.tabs.map(tab => createTabElement(tab)).join('')}
    </div>
  `;

  // 绑定窗口组事件
  bindWindowGroupEvents(groupDiv);

  return groupDiv;
}

/**
 * 创建标签页元素
 */
function createTabElement(tab) {
  const isSelected = selectedTabs.has(tab.id);
  const faviconUrl = tab.favIconUrl || '';
  
  // 构建标签页状态类
  const statusClasses = [
    tab.active ? 'active' : '',
    isSelected ? 'selected' : ''
  ].filter(Boolean).join(' ');
  
  return `
    <div class="tab-item ${statusClasses}" 
         data-tab-id="${tab.id}" data-window-id="${tab.windowId}">
      <input type="checkbox" class="tab-checkbox" ${isSelected ? 'checked' : ''}>
      <img class="tab-favicon" src="${faviconUrl}" 
           onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
      <div class="tab-favicon placeholder" style="display: none;">🌐</div>
      <div class="tab-info">
        <div class="tab-title" title="${escapeHtml(tab.title)}">${escapeHtml(tab.title)}</div>
        <div class="tab-url" title="${escapeHtml(tab.url)}">${escapeHtml(shortenUrl(tab.url))}</div>
      </div>
      <div class="tab-actions">
        <button class="tab-action-btn" title="在新窗口打开" data-action="open-new-window">
          <span class="icon">🔗</span>
        </button>
        <button class="tab-action-btn" title="在当前窗口打开" data-action="open-current-window">
          <span class="icon">📄</span>
        </button>
        <button class="tab-action-btn" title="关闭标签页" data-action="close-tab">
          <span class="icon">✕</span>
        </button>
      </div>
    </div>
  `;
}

/**
 * 绑定窗口组事件
 */
function bindWindowGroupEvents(groupElement) {
  const header = groupElement.querySelector('.window-group-header');
  const tabList = groupElement.querySelector('.tab-list');
  const windowId = parseInt(header.dataset.windowId);

  // 头部点击事件（展开/折叠）
  header.addEventListener('click', (e) => {
    if (e.target.closest('.window-group-actions')) return;
    toggleGroupCollapse(windowId);
  });

  // 窗口组操作按钮
  header.addEventListener('click', (e) => {
    const action = e.target.closest('[data-action]')?.dataset.action;
    if (!action) return;

    e.stopPropagation();
    
    switch (action) {
      case 'rename':
        renameWindowGroup(windowId);
        break;
      case 'close-window':
        closeWindow(windowId);
        break;
    }
  });

  // 标签页事件
  tabList.addEventListener('click', (e) => {
    const tabItem = e.target.closest('.tab-item');
    if (!tabItem) return;

    const tabId = parseInt(tabItem.dataset.tabId);
    const action = e.target.closest('[data-action]')?.dataset.action;

    if (e.target.type === 'checkbox') {
      // 复选框点击
      toggleTabSelection(tabId);
      return;
    }

    if (action) {
      // 操作按钮点击
      e.stopPropagation();
      handleTabAction(action, tabId);
      return;
    }

    // 标签页本身点击（激活）
    activateTab(tabId);
  });
}

/**
 * 切换窗口组折叠状态
 */
function toggleGroupCollapse(windowId) {
  const isCollapsed = collapsedGroups.has(windowId);
  
  if (isCollapsed) {
    collapsedGroups.delete(windowId);
  } else {
    collapsedGroups.add(windowId);
  }

  // 更新UI
  const groupElement = document.querySelector(`[data-window-id="${windowId}"]`);
  const collapseIcon = groupElement.querySelector('.collapse-icon');
  const tabList = groupElement.querySelector('.tab-list');

  collapseIcon.classList.toggle('collapsed', !isCollapsed);
  tabList.classList.toggle('collapsed', !isCollapsed);

  // 保存状态
  saveCollapsedGroups();
}

let searchTimeout = null; // 搜索防抖定时器

/**
 * 防抖函数
 */
function debounce(func, wait) {
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(searchTimeout);
      func(...args);
    };
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(later, wait);
  };
}

/**
 * 处理搜索（防抖版本）
 */
const handleSearchDebounced = debounce((query) => {
  searchQuery = query.trim();
  
  if (searchQuery === '') {
    // 清空搜索，显示所有窗口组
    refreshData();
  } else {
    // 过滤窗口组
    const filteredGroups = filterWindowGroups(windowGroups, searchQuery);
    windowGroups = filteredGroups;
    renderWindowGroups();
    updateStats();
  }
}, 300);

/**
 * 处理搜索
 */
function handleSearch(query) {
  handleSearchDebounced(query);
}

/**
 * 过滤窗口组（搜索功能）
 */
function filterWindowGroups(groups, query) {
  const lowerQuery = query.toLowerCase();
  
  return groups.map(group => {
    const filteredTabs = group.tabs.filter(tab => 
      tab.title.toLowerCase().includes(lowerQuery) ||
      tab.url.toLowerCase().includes(lowerQuery)
    );
    
    return {
      ...group,
      tabs: filteredTabs
    };
  }).filter(group => group.tabs.length > 0);
}

/**
 * 更新统计信息
 */
function updateStats() {
  const windowCount = windowGroups.length;
  const tabCount = getTotalTabCount();
  const memorySaved = calculateMemorySaved(tabCount);

  elements.windowCount.textContent = windowCount;
  elements.tabCount.textContent = tabCount;
  elements.memorySaved.textContent = `${memorySaved}%`;
}

/**
 * 计算总标签页数量
 */
function getTotalTabCount() {
  return windowGroups.reduce((total, group) => total + group.tabs.length, 0);
}

/**
 * 计算内存节省百分比（估算）
 */
function calculateMemorySaved(tabCount) {
  // 简单估算：每个标签页节省约60-80%的内存
  const baseMemorySaving = 70;
  const efficiency = Math.min(95, baseMemorySaving + (tabCount * 0.5));
  return Math.round(efficiency);
}

/**
 * 标签页选择相关方法
 */
function toggleTabSelection(tabId) {
  if (selectedTabs.has(tabId)) {
    selectedTabs.delete(tabId);
  } else {
    selectedTabs.add(tabId);
  }
  
  updateSelectionUI();
  updateBatchToolbar();
}

function clearSelection() {
  selectedTabs.clear();
  updateSelectionUI();
  updateBatchToolbar();
}

function updateSelectionUI() {
  // 更新所有复选框状态
  document.querySelectorAll('.tab-item').forEach(item => {
    const tabId = parseInt(item.dataset.tabId);
    const checkbox = item.querySelector('.tab-checkbox');
    const isSelected = selectedTabs.has(tabId);
    
    checkbox.checked = isSelected;
    item.classList.toggle('selected', isSelected);
  });
}

function updateBatchToolbar() {
  const count = selectedTabs.size;
  const toolbar = elements.batchToolbar;
  
  if (count > 0) {
    toolbar.style.display = 'flex';
    elements.selectedCount.textContent = count;
  } else {
    toolbar.style.display = 'none';
  }
}

/**
 * 工具方法
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function shortenUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname + urlObj.pathname;
  } catch {
    return url.length > 50 ? url.substring(0, 50) + '...' : url;
  }
}

function setStatus(text) {
  elements.statusText.textContent = text;
}

function updateLastUpdateTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
  elements.lastUpdate.textContent = `最后更新: ${timeString}`;
}

/**
 * 显示用户友好的错误消息
 */
function showError(message, isRetryable = false) {
  const errorContainer = document.createElement('div');
  errorContainer.className = 'error-notification';
  
  const errorContent = document.createElement('div');
  errorContent.className = 'error-content';
  
  const errorIcon = document.createElement('div');
  errorIcon.className = 'error-icon';
  errorIcon.textContent = '⚠️';
  
  const errorMessage = document.createElement('div');
  errorMessage.className = 'error-message';
  errorMessage.textContent = message;
  
  errorContent.appendChild(errorIcon);
  errorContent.appendChild(errorMessage);
  
  if (isRetryable) {
    const retryBtn = document.createElement('button');
    retryBtn.className = 'error-retry-btn';
    retryBtn.textContent = '重试';
    retryBtn.addEventListener('click', () => {
      errorContainer.remove();
      refreshData();
    });
    errorContent.appendChild(retryBtn);
  }
  
  const closeBtn = document.createElement('button');
  closeBtn.className = 'error-close-btn';
  closeBtn.textContent = '✕';
  closeBtn.addEventListener('click', () => {
    errorContainer.remove();
  });
  errorContent.appendChild(closeBtn);
  
  errorContainer.appendChild(errorContent);
  
  // 添加到页面顶部
  const appContainer = document.querySelector('.app-container');
  if (appContainer) {
    appContainer.insertBefore(errorContainer, appContainer.firstChild);
  }
  
  // 5秒后自动消失
  setTimeout(() => {
    if (errorContainer.parentElement) {
      errorContainer.remove();
    }
  }, 5000);
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
  const successContainer = document.createElement('div');
  successContainer.className = 'success-notification';
  
  const successContent = document.createElement('div');
  successContent.className = 'success-content';
  
  const successIcon = document.createElement('div');
  successIcon.className = 'success-icon';
  successIcon.textContent = '✅';
  
  const successMessage = document.createElement('div');
  successMessage.className = 'success-message';
  successMessage.textContent = message;
  
  const closeBtn = document.createElement('button');
  closeBtn.className = 'success-close-btn';
  closeBtn.textContent = '✕';
  closeBtn.addEventListener('click', () => {
    successContainer.remove();
  });
  
  successContent.appendChild(successIcon);
  successContent.appendChild(successMessage);
  successContent.appendChild(closeBtn);
  successContainer.appendChild(successContent);
  
  const appContainer = document.querySelector('.app-container');
  if (appContainer) {
    appContainer.insertBefore(successContainer, appContainer.firstChild);
  }
  
  // 3秒后自动消失
  setTimeout(() => {
    if (successContainer.parentElement) {
      successContainer.remove();
    }
  }, 3000);
}

/**
 * 检查Chrome扩展权限
 */
function checkChromePermissions() {
  if (!chrome || !chrome.tabs) {
    showError('Chrome扩展API不可用，请确保在正确的Chrome扩展环境中运行', false);
    return false;
  }
  return true;
}

/**
 * 安全的Chrome API调用包装器
 */
async function safeChromAPICall(apiCall, errorMessage = '操作失败', isRetryable = true) {
  try {
    if (!checkChromePermissions()) {
      throw new Error('Chrome权限不足');
    }
    
    return await apiCall();
  } catch (error) {
    console.error(errorMessage, error);
    
    // 根据错误类型显示不同的用户友好消息
    let userMessage = errorMessage;
    if (error.message.includes('permission')) {
      userMessage = '权限不足，请检查扩展权限设置';
      isRetryable = false;
    } else if (error.message.includes('network')) {
      userMessage = '网络连接问题，请检查网络连接';
    } else if (error.message.includes('storage')) {
      userMessage = '存储空间不足或存储错误';
    }
    
    showError(userMessage, isRetryable);
    throw error;
  }
}

/**
 * 存储和设置相关方法
 */
async function loadUserSettings() {
  try {
    const result = await chrome.storage.local.get(['userSettings']);
    userSettings = result.userSettings || getDefaultSettings();
  } catch (error) {
    console.error('加载用户设置失败:', error);
    userSettings = getDefaultSettings();
  }
}

function getDefaultSettings() {
  return {
    autoCollapse: false,
    showFavicons: true,
    showTabCount: true,
    sortOrder: 'recent',
    theme: 'light',
    compactMode: false
  };
}

async function loadCollapsedGroups() {
  try {
    const result = await chrome.storage.local.get(['collapsedGroups']);
    const collapsedData = result.collapsedGroups || {};
    collapsedGroups = new Set(Object.keys(collapsedData).map(id => parseInt(id)));
  } catch (error) {
    console.error('加载折叠状态失败:', error);
    collapsedGroups = new Set();
  }
}

async function saveCollapsedGroups() {
  try {
    const collapsedData = {};
    for (const windowId of collapsedGroups) {
      collapsedData[windowId] = true;
    }
    await chrome.storage.local.set({ collapsedGroups: collapsedData });
  } catch (error) {
    console.error('保存折叠状态失败:', error);
  }
}

/**
 * 标签页操作方法
 */
async function activateTab(tabId) {
  try {
    await chrome.tabs.update(tabId, { active: true });
    const tab = await chrome.tabs.get(tabId);
    await chrome.windows.update(tab.windowId, { focused: true });
    
    setStatus('标签页已激活');
    // 关闭popup
    window.close();
  } catch (error) {
    console.error('激活标签页失败:', error);
    setStatus('激活标签页失败');
  }
}

async function handleTabAction(action, tabId) {
  try {
    const tab = findTabById(tabId);
    if (!tab) {
      setStatus('标签页不存在');
      return;
    }

    switch (action) {
      case 'open-new-window':
        await chrome.windows.create({ url: tab.url });
        setStatus('已在新窗口打开');
        break;
        
      case 'open-current-window':
        await chrome.tabs.create({ url: tab.url });
        setStatus('已在当前窗口打开');
        break;
        
      case 'close-tab':
        await chrome.tabs.remove(tabId);
        setStatus('标签页已关闭');
        await refreshData();
        break;
        
      case 'copy-url':
        await navigator.clipboard.writeText(tab.url);
        setStatus('URL已复制');
        break;
        
      case 'copy-title':
        await navigator.clipboard.writeText(tab.title);
        setStatus('标题已复制');
        break;
    }
    
  } catch (error) {
    console.error('处理标签页操作失败:', error);
    setStatus('操作失败');
  }
}

async function renameWindowGroup(windowId) {
  try {
    const group = windowGroups.find(g => g.id === windowId);
    if (!group) return;

    const currentName = group.customName || group.title;
    const newName = prompt('请输入新的窗口组名称:', currentName);
    
    if (newName && newName !== currentName) {
      group.customName = newName;
      
      // 保存到存储
      const result = await chrome.storage.local.get(['customNames']);
      const customNames = result.customNames || {};
      customNames[windowId] = newName;
      await chrome.storage.local.set({ customNames });
      
      // 重新渲染
      renderWindowGroups();
      setStatus('窗口组已重命名');
    }
  } catch (error) {
    console.error('重命名窗口组失败:', error);
    setStatus('重命名失败');
  }
}

async function closeWindow(windowId) {
  try {
    const group = windowGroups.find(g => g.id === windowId);
    if (!group) return;

    const confirmed = confirm(`确定要关闭整个窗口组 "${group.customName || group.title}" 吗？`);
    if (!confirmed) return;

    await chrome.windows.remove(windowId);
    setStatus('窗口已关闭');
    await refreshData();
  } catch (error) {
    console.error('关闭窗口失败:', error);
    setStatus('关闭窗口失败');
  }
}

/**
 * 批量操作方法
 */
async function batchOpenInNewWindow() {
  try {
    const selectedTabIds = Array.from(selectedTabs);
    if (selectedTabIds.length === 0) return;

    setStatus('正在批量打开...');
    
    const tabs = selectedTabIds.map(id => findTabById(id)).filter(Boolean);
    const urls = tabs.map(tab => tab.url);
    
    await chrome.windows.create({ url: urls });
    
    setStatus(`已在新窗口打开 ${tabs.length} 个标签页`);
    clearSelection();
    
  } catch (error) {
    console.error('批量打开失败:', error);
    setStatus('批量打开失败');
  }
}

async function batchOpenInCurrentWindow() {
  try {
    const selectedTabIds = Array.from(selectedTabs);
    if (selectedTabIds.length === 0) return;

    setStatus('正在批量打开...');
    
    const tabs = selectedTabIds.map(id => findTabById(id)).filter(Boolean);
    
    for (const tab of tabs) {
      await chrome.tabs.create({ url: tab.url });
    }
    
    setStatus(`已在当前窗口打开 ${tabs.length} 个标签页`);
    clearSelection();
    
  } catch (error) {
    console.error('批量打开失败:', error);
    setStatus('批量打开失败');
  }
}

async function batchCloseTabs() {
  try {
    const selectedTabIds = Array.from(selectedTabs);
    if (selectedTabIds.length === 0) return;

    const confirmed = confirm(`确定要关闭选中的 ${selectedTabIds.length} 个标签页吗？`);
    if (!confirmed) return;

    setStatus('正在批量关闭...');
    
    await chrome.tabs.remove(selectedTabIds);
    setStatus(`已关闭 ${selectedTabIds.length} 个标签页`);
    clearSelection();
    await refreshData();
    
  } catch (error) {
    console.error('批量关闭失败:', error);
    setStatus('批量关闭失败');
  }
}

/**
 * 辅助方法
 */
function findTabById(tabId) {
  for (const group of windowGroups) {
    const tab = group.tabs.find(t => t.id === tabId);
    if (tab) return tab;
  }
  return null;
}

function applyCollapsedStates() {
  windowGroups.forEach(group => {
    group.isCollapsed = collapsedGroups.has(group.id);
  });
}

function focusSearch() {
  elements.searchInput.focus();
}

function openSettings() {
  chrome.runtime.openOptionsPage();
}

function handleKeyboard(e) {
  // Ctrl+F 或 Cmd+F: 聚焦搜索
  if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
    e.preventDefault();
    focusSearch();
    return;
  }
  
  // Escape: 清除搜索或关闭popup
  if (e.key === 'Escape') {
    if (elements.searchInput.value) {
      elements.searchInput.value = '';
      handleSearch('');
    } else {
      window.close();
    }
    return;
  }
  
  // F5: 刷新数据
  if (e.key === 'F5') {
    e.preventDefault();
    refreshData();
    return;
  }
  
  // Ctrl+A: 全选/取消全选
  if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
    e.preventDefault();
    toggleSelectAll();
    return;
  }
}

function handleContextMenu(e) {
  // 检查是否在标签页项上右键
  const tabItem = e.target.closest('.tab-item');
  if (!tabItem) {
    hideContextMenu();
    return;
  }
  
  e.preventDefault();
  
  const tabId = parseInt(tabItem.dataset.tabId);
  const contextMenu = elements.contextMenu;
  
  // 设置菜单位置
  contextMenu.style.left = e.pageX + 'px';
  contextMenu.style.top = e.pageY + 'px';
  contextMenu.style.display = 'block';
  
  // 保存当前操作的标签页ID
  contextMenu.dataset.tabId = tabId;
  
  // 绑定菜单项点击事件
  contextMenu.onclick = (menuEvent) => {
    const action = menuEvent.target.dataset.action;
    if (action) {
      handleTabAction(action, tabId);
      hideContextMenu();
    }
  };
}

function hideContextMenu() {
  elements.contextMenu.style.display = 'none';
}

function closeModal() {
  elements.modal.style.display = 'none';
}

function toggleSelectAll() {
  const allTabs = getAllVisibleTabs();
  const allSelected = allTabs.every(tab => selectedTabs.has(tab.id));
  
  if (allSelected) {
    // 取消全选
    clearSelection();
  } else {
    // 全选
    allTabs.forEach(tab => selectedTabs.add(tab.id));
    updateSelectionUI();
    updateBatchToolbar();
  }
}

function getAllVisibleTabs() {
  const tabs = [];
  windowGroups.forEach(group => {
    tabs.push(...group.tabs);
  });
  return tabs;
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  initApp();
});

/**
 * 帮助系统功能
 */

/**
 * 检查是否首次使用
 */
async function checkFirstTimeUser() {
  try {
    const result = await chrome.storage.local.get(['firstTimeUser']);
    isFirstTime = result.firstTimeUser !== false; // 默认为首次使用
    if (isFirstTime) {
      showWelcomeGuide();
    }
  } catch (error) {
    console.error('检查首次使用状态失败:', error);
  }
}

/**
 * 显示欢迎引导
 */
function showWelcomeGuide() {
  const guideOverlay = document.createElement('div');
  guideOverlay.className = 'welcome-guide-overlay';
  guideOverlay.innerHTML = `
    <div class="welcome-guide">
      <div class="guide-header">
        <h2>🗂️ 欢迎使用Chrome标签页管理器</h2>
        <p>让我们快速了解一下主要功能</p>
      </div>
      
      <div class="guide-content">
        <div class="guide-step active" data-step="1">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3>基本功能</h3>
            <ul>
              <li>🔍 搜索标签页：在搜索框中输入关键词查找特定标签页</li>
              <li>📊 查看统计：顶部显示窗口数量、标签页数量和内存节省</li>
              <li>⚡ 快速刷新：点击刷新按钮更新标签页数据</li>
              <li>⚙️ 设置选项：点击设置按钮访问扩展设置</li>
            </ul>
          </div>
        </div>
        
        <div class="guide-step" data-step="2">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3>标签页操作</h3>
            <ul>
              <li>✅ 多选标签页：点击复选框选择多个标签页</li>
              <li>📁 窗口组管理：点击窗口组标题可折叠/展开</li>
              <li>🔗 快速打开：点击标签页直接激活，或使用操作按钮</li>
              <li>🗑️ 批量操作：选中多个标签页后进行批量操作</li>
            </ul>
          </div>
        </div>
        
        <div class="guide-step" data-step="3">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3>快捷键</h3>
            <ul>
              <li><kbd>Ctrl+F</kbd> 聚焦搜索框</li>
              <li><kbd>F5</kbd> 刷新数据</li>
              <li><kbd>Ctrl+A</kbd> 全选/取消全选</li>
              <li><kbd>Esc</kbd> 清除搜索或关闭弹窗</li>
            </ul>
          </div>
        </div>
        
        <div class="guide-step" data-step="4">
          <div class="step-number">4</div>
          <div class="step-content">
            <h3>高级功能</h3>
            <ul>
              <li>🏷️ 标签页分类：为标签页添加分类和标签</li>
              <li>📝 标签页备注：为重要标签页添加备注</li>
              <li>📊 使用统计：查看标签页使用情况分析</li>
              <li>💾 导入导出：备份和恢复标签页数据</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="guide-navigation">
        <button class="guide-btn guide-prev" disabled>上一步</button>
        <div class="guide-indicators">
          <span class="indicator active" data-step="1"></span>
          <span class="indicator" data-step="2"></span>
          <span class="indicator" data-step="3"></span>
          <span class="indicator" data-step="4"></span>
        </div>
        <button class="guide-btn guide-next">下一步</button>
      </div>
      
      <div class="guide-footer">
        <label class="guide-checkbox">
          <input type="checkbox" id="dontShowAgain"> 不再显示此引导
        </label>
        <button class="guide-btn guide-close">开始使用</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(guideOverlay);
  
  // 绑定引导事件
  bindGuideEvents(guideOverlay);
}

/**
 * 绑定引导事件
 */
function bindGuideEvents(guideOverlay) {
  const steps = guideOverlay.querySelectorAll('.guide-step');
  const indicators = guideOverlay.querySelectorAll('.indicator');
  const prevBtn = guideOverlay.querySelector('.guide-prev');
  const nextBtn = guideOverlay.querySelector('.guide-next');
  const closeBtn = guideOverlay.querySelector('.guide-close');
  const dontShowAgain = guideOverlay.querySelector('#dontShowAgain');
  
  let currentStep = 1;
  const totalSteps = steps.length;
  
  // 更新步骤显示
  function updateStep() {
    steps.forEach(step => {
      step.classList.toggle('active', parseInt(step.dataset.step) === currentStep);
    });
    
    indicators.forEach(indicator => {
      indicator.classList.toggle('active', parseInt(indicator.dataset.step) === currentStep);
    });
    
    prevBtn.disabled = currentStep === 1;
    nextBtn.textContent = currentStep === totalSteps ? '完成' : '下一步';
  }
  
  // 上一步
  prevBtn.addEventListener('click', () => {
    if (currentStep > 1) {
      currentStep--;
      updateStep();
    }
  });
  
  // 下一步
  nextBtn.addEventListener('click', () => {
    if (currentStep < totalSteps) {
      currentStep++;
      updateStep();
    } else {
      closeGuide();
    }
  });
  
  // 指示器点击
  indicators.forEach(indicator => {
    indicator.addEventListener('click', () => {
      currentStep = parseInt(indicator.dataset.step);
      updateStep();
    });
  });
  
  // 关闭引导
  function closeGuide() {
    if (dontShowAgain.checked) {
      chrome.storage.local.set({ firstTimeUser: false });
    }
    guideOverlay.remove();
  }
  
  closeBtn.addEventListener('click', closeGuide);
  
  // 点击背景关闭
  guideOverlay.addEventListener('click', (e) => {
    if (e.target === guideOverlay) {
      closeGuide();
    }
  });
}

/**
 * 显示帮助面板
 */
function showHelpPanel() {
  const helpPanel = document.createElement('div');
  helpPanel.className = 'help-panel';
  helpPanel.innerHTML = `
    <div class="help-backdrop"></div>
    <div class="help-content">
      <div class="help-header">
        <h3>🔍 帮助中心</h3>
        <button class="btn btn-icon help-close">✕</button>
      </div>
      
      <div class="help-body">
        <div class="help-tabs">
          <button class="help-tab active" data-tab="usage">使用说明</button>
          <button class="help-tab" data-tab="shortcuts">快捷键</button>
          <button class="help-tab" data-tab="features">功能介绍</button>
          <button class="help-tab" data-tab="troubleshooting">故障排除</button>
        </div>
        
        <div class="help-tab-content active" id="usage-content">
          <h4>基本操作</h4>
          <div class="help-section">
            <h5>🔍 搜索标签页</h5>
            <p>在搜索框中输入关键词，系统会实时过滤匹配的标签页。支持搜索标签页标题和URL。</p>
          </div>
          
          <div class="help-section">
            <h5>📁 窗口组管理</h5>
            <p>每个浏览器窗口显示为一个窗口组。点击窗口组标题可以折叠/展开标签页列表。</p>
          </div>
          
          <div class="help-section">
            <h5>✅ 批量操作</h5>
            <p>选中多个标签页后，会显示批量操作工具栏，可以进行批量打开、关闭等操作。</p>
          </div>
          
          <div class="help-section">
            <h5>🔄 数据刷新</h5>
            <p>点击刷新按钮可以更新标签页数据。系统会自动检测标签页变化。</p>
          </div>
        </div>
        
        <div class="help-tab-content" id="shortcuts-content">
          <h4>快捷键列表</h4>
          <div class="shortcuts-grid">
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>Ctrl</kbd> + <kbd>F</kbd></div>
              <div class="shortcut-desc">聚焦搜索框</div>
            </div>
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>F5</kbd></div>
              <div class="shortcut-desc">刷新数据</div>
            </div>
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>Ctrl</kbd> + <kbd>A</kbd></div>
              <div class="shortcut-desc">全选/取消全选</div>
            </div>
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>Esc</kbd></div>
              <div class="shortcut-desc">清除搜索或关闭弹窗</div>
            </div>
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>Enter</kbd></div>
              <div class="shortcut-desc">激活选中的标签页</div>
            </div>
            <div class="shortcut-item">
              <div class="shortcut-key"><kbd>Del</kbd></div>
              <div class="shortcut-desc">关闭选中的标签页</div>
            </div>
          </div>
        </div>
        
        <div class="help-tab-content" id="features-content">
          <h4>功能特色</h4>
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">🔍</div>
              <div class="feature-info">
                <h5>智能搜索</h5>
                <p>支持标题和URL搜索，实时过滤结果</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <div class="feature-info">
                <h5>使用统计</h5>
                <p>显示窗口数量、标签页数量和内存节省</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <div class="feature-info">
                <h5>快速操作</h5>
                <p>一键打开、关闭、切换标签页</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🗂️</div>
              <div class="feature-info">
                <h5>窗口组织</h5>
                <p>按窗口分组显示，支持折叠展开</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🏷️</div>
              <div class="feature-info">
                <h5>分类管理</h5>
                <p>为标签页添加分类和标签</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📝</div>
              <div class="feature-info">
                <h5>备注系统</h5>
                <p>为重要标签页添加备注说明</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="help-tab-content" id="troubleshooting-content">
          <h4>常见问题解决</h4>
          <div class="faq-list">
            <div class="faq-item">
              <h5>Q: 为什么某些标签页不显示？</h5>
              <p>A: 可能是由于权限限制或标签页类型不支持。请确保扩展有足够的权限访问所有标签页。</p>
            </div>
            <div class="faq-item">
              <h5>Q: 批量操作没有响应？</h5>
              <p>A: 请先选中至少一个标签页，然后使用批量操作工具栏中的按钮。</p>
            </div>
            <div class="faq-item">
              <h5>Q: 搜索功能不工作？</h5>
              <p>A: 请检查搜索框是否正确输入，并确保有匹配的标签页。可以尝试刷新数据后重新搜索。</p>
            </div>
            <div class="faq-item">
              <h5>Q: 如何重置扩展设置？</h5>
              <p>A: 在Chrome扩展管理页面找到此扩展，点击详情，然后清除存储数据。</p>
            </div>
            <div class="faq-item">
              <h5>Q: 数据没有及时更新？</h5>
              <p>A: 点击刷新按钮手动更新数据，或检查是否有其他扩展干扰。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="help-footer">
        <button class="btn btn-primary" id="showWelcomeGuideBtn">重新显示引导</button>
        <button class="btn btn-secondary help-close-btn">关闭帮助</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(helpPanel);
  
  // 绑定帮助面板事件
  bindHelpPanelEvents(helpPanel);
}

/**
 * 绑定帮助面板事件
 */
function bindHelpPanelEvents(helpPanel) {
  const tabs = helpPanel.querySelectorAll('.help-tab');
  const contents = helpPanel.querySelectorAll('.help-tab-content');
  const closeBtn = helpPanel.querySelector('.help-close');
  const closeBtnFooter = helpPanel.querySelector('.help-close-btn');
  const backdrop = helpPanel.querySelector('.help-backdrop');
  const showWelcomeGuideBtn = helpPanel.querySelector('#showWelcomeGuideBtn');
  
  // 标签页切换
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const tabId = tab.dataset.tab;
      
      tabs.forEach(t => t.classList.remove('active'));
      contents.forEach(c => c.classList.remove('active'));
      
      tab.classList.add('active');
      helpPanel.querySelector(`#${tabId}-content`).classList.add('active');
    });
  });
  
  // 关闭帮助面板
  function closeHelp() {
    helpPanel.remove();
  }
  
  closeBtn.addEventListener('click', closeHelp);
  closeBtnFooter.addEventListener('click', closeHelp);
  backdrop.addEventListener('click', closeHelp);
  
  // 重新显示引导
  if (showWelcomeGuideBtn) {
    showWelcomeGuideBtn.addEventListener('click', () => {
      closeHelp();
      showWelcomeGuide();
    });
  }
}

/**
 * 添加帮助按钮到工具栏
 */
function addHelpButton() {
  const toolbarRight = document.querySelector('.toolbar-right');
  if (!toolbarRight) return;
  
  const helpBtn = document.createElement('button');
  helpBtn.className = 'btn btn-icon help-button';
  helpBtn.innerHTML = '<span class="icon">❓</span>';
  helpBtn.title = '帮助';
  helpBtn.addEventListener('click', showHelpPanel);
  
  toolbarRight.appendChild(helpBtn);
}

/**
 * 显示功能提示
 */
function showFeatureTips() {
  const tips = [
    {
      element: '.search-input',
      title: '智能搜索',
      content: '输入关键词快速找到目标标签页，支持标题和URL搜索'
    },
    {
      element: '.stats-bar',
      title: '使用统计',
      content: '实时显示窗口数量、标签页数量和估算的内存节省'
    },
    {
      element: '.window-group-header',
      title: '窗口组管理',
      content: '点击标题可折叠/展开标签页列表，右侧按钮可重命名或关闭窗口'
    },
    {
      element: '.tab-checkbox',
      title: '批量操作',
      content: '选中多个标签页后可进行批量打开、关闭等操作'
    }
  ];
  
  // 这里可以实现工具提示功能
  // 暂时通过console.log展示
  console.log('功能提示已准备就绪:', tips);
}