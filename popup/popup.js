// 导入必要的模块
import ChromeAPI from '../utils/chromeAPI.js';
import StorageManager from '../utils/storageManager.js';
import TabNotesManager from '../utils/tabNotesManager.js';
import DragDropManager from '../utils/dragDropManager.js';
import UndoManager from '../utils/undoManager.js';
import DuplicateTabManager from '../utils/duplicateTabManager.js';
import SearchFilterManager from '../utils/searchFilterManager.js';
import ImportExportManager from '../utils/importExportManager.js';
import TabPreviewManager from '../utils/tabPreviewManager.js';
import CustomGroupManager from '../utils/customGroupManager.js';
import BatchOperationsManager from '../utils/batchOperationsManager.js';
import TabStatisticsManager from '../utils/tabStatisticsManager.js';

// 初始化全局对象
const chromeAPI = new ChromeAPI();
const storageManager = new StorageManager();

/**
 * Chrome标签页管理器 - 主控制器
 * 负责协调数据收集、界面渲染和用户交互
 */
class TabManagerApp {
  constructor() {
    this.windowGroups = [];
    this.selectedTabs = new Set();
    this.searchQuery = '';
    this.collapsedGroups = new Set();
    this.isLoading = false;
    
    // 初始化管理器
    this.tabNotesManager = new TabNotesManager(this);
    this.dragDropManager = new DragDropManager(this);
    this.searchFilterManager = new SearchFilterManager(this);
    this.importExportManager = new ImportExportManager(this);
    this.tabPreviewManager = new TabPreviewManager(this);
    this.customGroupManager = new CustomGroupManager(this);
    this.batchOperationsManager = new BatchOperationsManager(this);
    this.tabStatisticsManager = new TabStatisticsManager(this);
    
    this.elements = {};
    this.bindElements();
    this.bindEvents();
    this.init();
  }

  /**
   * 绑定DOM元素引用
   */
  bindElements() {
    this.elements = {
      // 工具栏元素
      refreshBtn: document.getElementById('refreshBtn'),
      searchInput: document.getElementById('searchInput'),
      searchBtn: document.getElementById('searchBtn'),
      settingsBtn: document.getElementById('settingsBtn'),
      
      // 统计信息
      windowCount: document.getElementById('windowCount'),
      tabCount: document.getElementById('tabCount'),
      memorySaved: document.getElementById('memorySaved'),
      
      // 批量操作
      batchToolbar: document.getElementById('batchToolbar'),
      selectedCount: document.getElementById('selectedCount'),
      batchOpenNewWindow: document.getElementById('batchOpenNewWindow'),
      batchOpenCurrentWindow: document.getElementById('batchOpenCurrentWindow'),
      batchClose: document.getElementById('batchClose'),
      clearSelection: document.getElementById('clearSelection'),
      
      // 主内容
      loadingIndicator: document.getElementById('loadingIndicator'),
      emptyState: document.getElementById('emptyState'),
      windowGroups: document.getElementById('windowGroups'),
      
      // 状态栏
      statusText: document.getElementById('statusText'),
      lastUpdate: document.getElementById('lastUpdate'),
      
      // 右键菜单和模态框
      contextMenu: document.getElementById('contextMenu'),
      modal: document.getElementById('modal'),
      modalTitle: document.getElementById('modalTitle'),
      modalBody: document.getElementById('modalBody'),
      modalClose: document.getElementById('modalClose'),
      modalCancel: document.getElementById('modalCancel'),
      modalConfirm: document.getElementById('modalConfirm')
    };
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 工具栏事件
    this.elements.refreshBtn.addEventListener('click', () => this.refreshData());
    this.elements.searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
    this.elements.searchBtn.addEventListener('click', () => this.focusSearch());
    this.elements.settingsBtn.addEventListener('click', () => this.openSettings());
    
    // 批量操作事件
    this.elements.batchOpenNewWindow.addEventListener('click', () => this.batchOpenInNewWindow());
    this.elements.batchOpenCurrentWindow.addEventListener('click', () => this.batchOpenInCurrentWindow());
    this.elements.batchClose.addEventListener('click', () => this.batchCloseTabs());
    this.elements.clearSelection.addEventListener('click', () => this.clearSelection());
    
    // 模态框事件
    this.elements.modalClose.addEventListener('click', () => this.closeModal());
    this.elements.modalCancel.addEventListener('click', () => this.closeModal());
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    
    // 右键菜单
    document.addEventListener('contextmenu', (e) => this.handleContextMenu(e));
    document.addEventListener('click', () => this.hideContextMenu());
    
    // 搜索快捷键
    this.elements.searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.elements.searchInput.value = '';
        this.handleSearch('');
      }
    });
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      this.setStatus('正在初始化...');
      await this.loadUserSettings();
      await this.loadCollapsedGroups();
      await this.tabNotesManager.loadTabMetadata();
      await this.refreshData();
      this.setStatus('就绪');
    } catch (error) {
      console.error('初始化失败:', error);
      this.setStatus('初始化失败');
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    if (this.isLoading) return;
    
    try {
      this.isLoading = true;
      this.showLoading(true);
      this.setStatus('正在收集标签页数据...');
      
      // 获取所有窗口和标签页
      const windowGroups = await chromeAPI.getAllTabsGroupedByWindow();
      
      // 应用搜索过滤
      this.windowGroups = this.searchQuery ? 
        await this.filterWindowGroups(windowGroups, this.searchQuery) : 
        windowGroups;
      
      // 恢复折叠状态
      this.applyCollapsedStates();
      
      // 渲染界面
      this.renderWindowGroups();
      this.updateStats();
      this.updateLastUpdateTime();
      
      this.setStatus('数据更新完成');
      
    } catch (error) {
      console.error('刷新数据失败:', error);
      this.setStatus('数据更新失败');
      this.showError('无法获取标签页数据，请检查权限设置');
    } finally {
      this.isLoading = false;
      this.showLoading(false);
    }
  }

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    this.elements.loadingIndicator.style.display = show ? 'flex' : 'none';
    this.elements.windowGroups.style.display = show ? 'none' : 'block';
    this.elements.emptyState.style.display = 'none';
  }

  /**
   * 渲染窗口组
   */
  renderWindowGroups() {
    const container = this.elements.windowGroups;
    container.innerHTML = '';

    if (this.windowGroups.length === 0) {
      this.elements.emptyState.style.display = 'flex';
      this.elements.windowGroups.style.display = 'none';
      return;
    }

    this.elements.emptyState.style.display = 'none';
    this.elements.windowGroups.style.display = 'block';

    this.windowGroups.forEach((group, index) => {
      const groupElement = this.createWindowGroupElement(group, index);
      container.appendChild(groupElement);
    });
    
    // 启用拖拽功能
    this.enableDragAndDrop();
  }

  /**
   * 启用拖拽功能
   */
  enableDragAndDrop() {
    // 更新拖拽管理器中的可拖拽元素
    this.dragDropManager.updateDragableElements();
    
    // 为新创建的元素启用拖拽
    document.querySelectorAll('.tab-item').forEach(item => {
      item.draggable = true;
    });
    
    document.querySelectorAll('.window-group-header').forEach(header => {
      header.draggable = true;
    });
  }

  /**
   * 创建窗口组元素
   */
  createWindowGroupElement(group, index) {
    const groupDiv = document.createElement('div');
    groupDiv.className = `window-group ${group.focused ? 'focused' : ''}`;
    groupDiv.dataset.windowId = group.id;

    const isCollapsed = this.collapsedGroups.has(group.id);
    
    groupDiv.innerHTML = `
      <div class="window-group-header" data-window-id="${group.id}">
        <div class="window-group-info">
          <span class="collapse-icon ${isCollapsed ? 'collapsed' : ''}">▼</span>
          <span class="window-group-title">${group.customName || group.title}</span>
          <span class="window-group-count">${group.tabs.length}</span>
          ${group.incognito ? '<span class="incognito-badge">🕶️</span>' : ''}
        </div>
        <div class="window-group-actions">
          <button class="btn btn-icon btn-small" title="重命名窗口组" data-action="rename">
            <span class="icon">✏️</span>
          </button>
          <button class="btn btn-icon btn-small" title="关闭整个窗口" data-action="close-window">
            <span class="icon">✕</span>
          </button>
        </div>
      </div>
      <div class="tab-list ${isCollapsed ? 'collapsed' : ''}" data-window-id="${group.id}">
        ${group.tabs.map(tab => this.createTabElement(tab)).join('')}
      </div>
    `;

    // 绑定窗口组事件
    this.bindWindowGroupEvents(groupDiv);

    return groupDiv;
  }

  /**
   * 创建标签页元素
   */
  createTabElement(tab) {
    const isSelected = this.selectedTabs.has(tab.id);
    const faviconUrl = tab.favIconUrl || '';
    
    // 获取标签页元数据
    const metadata = this.tabNotesManager.getTabMetadata(tab.id);
    const category = metadata.category ? this.tabNotesManager.categories.get(metadata.category) : null;
    
    // 构建标签页状态类
    const statusClasses = [
      tab.active ? 'active' : '',
      isSelected ? 'selected' : '',
      metadata.isBookmarked ? 'bookmarked' : '',
      metadata.isArchived ? 'archived' : ''
    ].filter(Boolean).join(' ');
    
    return `
      <div class="tab-item ${statusClasses}" 
           data-tab-id="${tab.id}" data-window-id="${tab.windowId}">
        ${category ? `<div class="tab-category-indicator" style="background-color: ${category.color}"></div>` : ''}
        <input type="checkbox" class="tab-checkbox" ${isSelected ? 'checked' : ''}>
        <img class="tab-favicon" src="${faviconUrl}" 
             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
        <div class="tab-favicon placeholder" style="display: none;">🌐</div>
        <div class="tab-info">
          <div class="tab-title" title="${this.escapeHtml(tab.title)}">${this.escapeHtml(tab.title)}</div>
          <div class="tab-url" title="${this.escapeHtml(tab.url)}">${this.escapeHtml(this.shortenUrl(tab.url))}</div>
          ${metadata.notes ? `<div class="tab-notes-preview">${this.escapeHtml(metadata.notes.substring(0, 50))}${metadata.notes.length > 50 ? '...' : ''}</div>` : ''}
          ${metadata.tags.length > 0 ? `
            <div class="tab-tags-display">
              ${metadata.tags.slice(0, 3).map(tag => `<span class="tab-tag-chip">${this.escapeHtml(tag)}</span>`).join('')}
              ${metadata.tags.length > 3 ? `<span class="tab-tag-chip">+${metadata.tags.length - 3}</span>` : ''}
            </div>
          ` : ''}
        </div>
        <div class="tab-metadata">
          ${metadata.priority > 3 ? `<div class="tab-priority-indicator" title="优先级: ${metadata.priority}">${'⭐'.repeat(metadata.priority - 3)}</div>` : ''}
          ${metadata.rating > 0 ? `<div class="tab-rating-indicator" title="评分: ${metadata.rating}">${'⭐'.repeat(metadata.rating)}</div>` : ''}
          ${metadata.isBookmarked ? `<div class="tab-bookmark-indicator" title="已收藏">📌</div>` : ''}
        </div>
        <div class="tab-actions">
          <button class="tab-action-btn" title="编辑标签页" data-action="edit-tab">
            <span class="icon">✏️</span>
          </button>
          <button class="tab-action-btn" title="在新窗口打开" data-action="open-new-window">
            <span class="icon">🔗</span>
          </button>
          <button class="tab-action-btn" title="在当前窗口打开" data-action="open-current-window">
            <span class="icon">📄</span>
          </button>
          <button class="tab-action-btn" title="关闭标签页" data-action="close-tab">
            <span class="icon">✕</span>
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 绑定窗口组事件
   */
  bindWindowGroupEvents(groupElement) {
    const header = groupElement.querySelector('.window-group-header');
    const tabList = groupElement.querySelector('.tab-list');
    const windowId = parseInt(header.dataset.windowId);

    // 头部点击事件（展开/折叠）
    header.addEventListener('click', (e) => {
      if (e.target.closest('.window-group-actions')) return;
      this.toggleGroupCollapse(windowId);
    });

    // 窗口组操作按钮
    header.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      if (!action) return;

      e.stopPropagation();
      
      switch (action) {
        case 'rename':
          this.renameWindowGroup(windowId);
          break;
        case 'close-window':
          this.closeWindow(windowId);
          break;
      }
    });

    // 标签页事件
    tabList.addEventListener('click', (e) => {
      const tabItem = e.target.closest('.tab-item');
      if (!tabItem) return;

      const tabId = parseInt(tabItem.dataset.tabId);
      const action = e.target.closest('[data-action]')?.dataset.action;

      if (e.target.type === 'checkbox') {
        // 复选框点击
        this.toggleTabSelection(tabId);
        return;
      }

      if (action) {
        // 操作按钮点击
        e.stopPropagation();
        this.handleTabAction(action, tabId);
        return;
      }

      // 标签页本身点击（激活）
      this.activateTab(tabId);
    });
  }

  /**
   * 切换窗口组折叠状态
   */
  toggleGroupCollapse(windowId) {
    const isCollapsed = this.collapsedGroups.has(windowId);
    
    if (isCollapsed) {
      this.collapsedGroups.delete(windowId);
    } else {
      this.collapsedGroups.add(windowId);
    }

    // 更新UI
    const groupElement = document.querySelector(`[data-window-id="${windowId}"]`);
    const collapseIcon = groupElement.querySelector('.collapse-icon');
    const tabList = groupElement.querySelector('.tab-list');

    collapseIcon.classList.toggle('collapsed', !isCollapsed);
    tabList.classList.toggle('collapsed', !isCollapsed);

    // 保存状态
    this.saveCollapsedGroups();
  }

  /**
   * 处理搜索
   */
  async handleSearch(query) {
    this.searchQuery = query.trim();
    
    if (this.searchQuery === '') {
      // 清空搜索，显示所有窗口组
      await this.refreshData();
    } else {
      // 使用高级搜索管理器
      this.searchFilterManager.handleBasicSearch(this.searchQuery);
    }
  }

  /**
   * 过滤窗口组（搜索功能）
   */
  async filterWindowGroups(windowGroups, query) {
    const lowerQuery = query.toLowerCase();
    
    return windowGroups.map(group => {
      const filteredTabs = group.tabs.filter(tab => 
        tab.title.toLowerCase().includes(lowerQuery) ||
        tab.url.toLowerCase().includes(lowerQuery)
      );
      
      return {
        ...group,
        tabs: filteredTabs
      };
    }).filter(group => group.tabs.length > 0);
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    const windowCount = this.windowGroups.length;
    const tabCount = this.getTotalTabCount();
    const memorySaved = this.calculateMemorySaved(tabCount);

    this.elements.windowCount.textContent = windowCount;
    this.elements.tabCount.textContent = tabCount;
    this.elements.memorySaved.textContent = `${memorySaved}%`;
  }

  /**
   * 计算总标签页数量
   */
  getTotalTabCount() {
    return this.windowGroups.reduce((total, group) => total + group.tabs.length, 0);
  }

  /**
   * 计算内存节省百分比（估算）
   */
  calculateMemorySaved(tabCount) {
    // 简单估算：每个标签页节省约60-80%的内存
    const baseMemorySaving = 70;
    const efficiency = Math.min(95, baseMemorySaving + (tabCount * 0.5));
    return Math.round(efficiency);
  }

  /**
   * 标签页选择相关方法
   */
  toggleTabSelection(tabId) {
    if (this.selectedTabs.has(tabId)) {
      this.selectedTabs.delete(tabId);
    } else {
      this.selectedTabs.add(tabId);
    }
    
    this.updateSelectionUI();
    this.updateBatchToolbar();
  }

  clearSelection() {
    this.selectedTabs.clear();
    this.updateSelectionUI();
    this.updateBatchToolbar();
  }

  updateSelectionUI() {
    // 更新所有复选框状态
    document.querySelectorAll('.tab-item').forEach(item => {
      const tabId = parseInt(item.dataset.tabId);
      const checkbox = item.querySelector('.tab-checkbox');
      const isSelected = this.selectedTabs.has(tabId);
      
      checkbox.checked = isSelected;
      item.classList.toggle('selected', isSelected);
    });
  }

  updateBatchToolbar() {
    const count = this.selectedTabs.size;
    const toolbar = this.elements.batchToolbar;
    
    if (count > 0) {
      toolbar.style.display = 'flex';
      this.elements.selectedCount.textContent = count;
    } else {
      toolbar.style.display = 'none';
    }
  }

  /**
   * 工具方法
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  shortenUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + urlObj.pathname;
    } catch {
      return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }
  }

  setStatus(text) {
    this.elements.statusText.textContent = text;
  }

  updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
    this.elements.lastUpdate.textContent = `最后更新: ${timeString}`;
  }

  showError(message) {
    // 简单的错误显示，后续可以改进为更好的UI
    alert(message);
  }

  /**
   * 存储和设置相关方法
   */
  async loadUserSettings() {
    try {
      this.userSettings = await storageManager.loadUserSettings();
    } catch (error) {
      console.error('加载用户设置失败:', error);
      this.userSettings = storageManager.getDefaultSettings();
    }
  }

  async loadCollapsedGroups() {
    try {
      const collapsedData = await storageManager.loadCollapsedGroups();
      this.collapsedGroups = new Set(Object.keys(collapsedData).map(id => parseInt(id)));
    } catch (error) {
      console.error('加载折叠状态失败:', error);
      this.collapsedGroups = new Set();
    }
  }

  async saveCollapsedGroups() {
    try {
      const collapsedData = {};
      for (const windowId of this.collapsedGroups) {
        collapsedData[windowId] = true;
      }
      await storageManager.saveCollapsedGroups(collapsedData);
    } catch (error) {
      console.error('保存折叠状态失败:', error);
    }
  }

  /**
   * 标签页操作方法
   */
  async activateTab(tabId) {
    try {
      const success = await this.sendMessage('ACTIVATE_TAB', { tabId });
      if (success) {
        this.setStatus('标签页已激活');
        // 关闭popup
        window.close();
      } else {
        this.setStatus('激活标签页失败');
      }
    } catch (error) {
      console.error('激活标签页失败:', error);
      this.setStatus('激活标签页失败');
    }
  }

  async handleTabAction(action, tabId) {
    try {
      const tab = this.findTabById(tabId);
      if (!tab) {
        this.setStatus('标签页不存在');
        return;
      }

      let success = false;
      
      switch (action) {
        case 'edit-tab':
          this.tabNotesManager.createTabEditDialog(tabId, tab);
          this.setStatus('正在编辑标签页');
          break;
          
        case 'open-new-window':
          success = await this.sendMessage('OPEN_TAB_NEW_WINDOW', { url: tab.url });
          if (success) this.setStatus('已在新窗口打开');
          break;
          
        case 'open-current-window':
          success = await this.sendMessage('OPEN_TAB_CURRENT_WINDOW', { url: tab.url });
          if (success) this.setStatus('已在当前窗口打开');
          break;
          
        case 'close-tab':
          success = await this.sendMessage('CLOSE_TAB', { tabId });
          if (success) {
            this.setStatus('标签页已关闭');
            await this.refreshData();
          }
          break;
          
        case 'copy-url':
          await navigator.clipboard.writeText(tab.url);
          this.setStatus('URL已复制');
          break;
          
        case 'copy-title':
          await navigator.clipboard.writeText(tab.title);
          this.setStatus('标题已复制');
          break;
      }
      
      if (!success && action !== 'copy-url' && action !== 'copy-title' && action !== 'edit-tab') {
        this.setStatus('操作失败');
      }
      
    } catch (error) {
      console.error('处理标签页操作失败:', error);
      this.setStatus('操作失败');
    }
  }

  async renameWindowGroup(windowId) {
    try {
      const group = this.windowGroups.find(g => g.id === windowId);
      if (!group) return;

      const currentName = group.customName || group.title;
      const newName = prompt('请输入新的窗口组名称:', currentName);
      
      if (newName && newName !== currentName) {
        group.customName = newName;
        
        // 保存到存储
        const customNames = await storageManager.loadCustomNames();
        customNames[windowId] = newName;
        await storageManager.saveCustomNames(customNames);
        
        // 重新渲染
        this.renderWindowGroups();
        this.setStatus('窗口组已重命名');
      }
    } catch (error) {
      console.error('重命名窗口组失败:', error);
      this.setStatus('重命名失败');
    }
  }

  async closeWindow(windowId) {
    try {
      const group = this.windowGroups.find(g => g.id === windowId);
      if (!group) return;

      const confirmed = confirm(`确定要关闭整个窗口组 "${group.customName || group.title}" 吗？`);
      if (!confirmed) return;

      const success = await this.sendMessage('CLOSE_WINDOW', { windowId });
      if (success) {
        this.setStatus('窗口已关闭');
        await this.refreshData();
      } else {
        this.setStatus('关闭窗口失败');
      }
    } catch (error) {
      console.error('关闭窗口失败:', error);
      this.setStatus('关闭窗口失败');
    }
  }

  /**
   * 批量操作方法
   */
  async batchOpenInNewWindow() {
    try {
      const selectedTabIds = Array.from(this.selectedTabs);
      if (selectedTabIds.length === 0) return;

      this.setStatus('正在批量打开...');
      
      const tabs = selectedTabIds.map(id => this.findTabById(id)).filter(Boolean);
      
      for (const tab of tabs) {
        await this.sendMessage('OPEN_TAB_NEW_WINDOW', { url: tab.url });
      }
      
      this.setStatus(`已在新窗口打开 ${tabs.length} 个标签页`);
      this.clearSelection();
      
    } catch (error) {
      console.error('批量打开失败:', error);
      this.setStatus('批量打开失败');
    }
  }

  async batchOpenInCurrentWindow() {
    try {
      const selectedTabIds = Array.from(this.selectedTabs);
      if (selectedTabIds.length === 0) return;

      this.setStatus('正在批量打开...');
      
      const tabs = selectedTabIds.map(id => this.findTabById(id)).filter(Boolean);
      
      for (const tab of tabs) {
        await this.sendMessage('OPEN_TAB_CURRENT_WINDOW', { url: tab.url });
      }
      
      this.setStatus(`已在当前窗口打开 ${tabs.length} 个标签页`);
      this.clearSelection();
      
    } catch (error) {
      console.error('批量打开失败:', error);
      this.setStatus('批量打开失败');
    }
  }

  async batchCloseTabs() {
    try {
      const selectedTabIds = Array.from(this.selectedTabs);
      if (selectedTabIds.length === 0) return;

      const confirmed = confirm(`确定要关闭选中的 ${selectedTabIds.length} 个标签页吗？`);
      if (!confirmed) return;

      this.setStatus('正在批量关闭...');
      
      const success = await this.sendMessage('CLOSE_TABS', { tabIds: selectedTabIds });
      if (success) {
        this.setStatus(`已关闭 ${selectedTabIds.length} 个标签页`);
        this.clearSelection();
        await this.refreshData();
      } else {
        this.setStatus('批量关闭失败');
      }
      
    } catch (error) {
      console.error('批量关闭失败:', error);
      this.setStatus('批量关闭失败');
    }
  }

  /**
   * 辅助方法
   */
  findTabById(tabId) {
    for (const group of this.windowGroups) {
      const tab = group.tabs.find(t => t.id === tabId);
      if (tab) return tab;
    }
    return null;
  }

  async sendMessage(type, data) {
    try {
      const response = await chrome.runtime.sendMessage({ type, ...data });
      return response.success;
    } catch (error) {
      console.error('发送消息失败:', error);
      return false;
    }
  }

  applyCollapsedStates() {
    this.windowGroups.forEach(group => {
      group.isCollapsed = this.collapsedGroups.has(group.id);
    });
  }

  focusSearch() {
    this.elements.searchInput.focus();
  }

  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  handleKeyboard(e) {
    // Ctrl+F 或 Cmd+F: 聚焦搜索
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
      e.preventDefault();
      this.focusSearch();
      return;
    }
    
    // Escape: 清除搜索或关闭popup
    if (e.key === 'Escape') {
      if (this.elements.searchInput.value) {
        this.elements.searchInput.value = '';
        this.handleSearch('');
      } else {
        window.close();
      }
      return;
    }
    
    // F5: 刷新数据
    if (e.key === 'F5') {
      e.preventDefault();
      this.refreshData();
      return;
    }
    
    // Ctrl+A: 全选/取消全选
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
      e.preventDefault();
      this.toggleSelectAll();
      return;
    }
  }

  handleContextMenu(e) {
    // 检查是否在标签页项上右键
    const tabItem = e.target.closest('.tab-item');
    if (!tabItem) {
      this.hideContextMenu();
      return;
    }
    
    e.preventDefault();
    
    const tabId = parseInt(tabItem.dataset.tabId);
    const contextMenu = this.elements.contextMenu;
    
    // 设置菜单位置
    contextMenu.style.left = e.pageX + 'px';
    contextMenu.style.top = e.pageY + 'px';
    contextMenu.style.display = 'block';
    
    // 保存当前操作的标签页ID
    contextMenu.dataset.tabId = tabId;
    
    // 绑定菜单项点击事件
    contextMenu.addEventListener('click', (menuEvent) => {
      const action = menuEvent.target.dataset.action;
      if (action) {
        this.handleTabAction(action, tabId);
        this.hideContextMenu();
      }
    });
  }

  hideContextMenu() {
    this.elements.contextMenu.style.display = 'none';
  }

  closeModal() {
    this.elements.modal.style.display = 'none';
  }

  toggleSelectAll() {
    const allTabs = this.getAllVisibleTabs();
    const allSelected = allTabs.every(tab => this.selectedTabs.has(tab.id));
    
    if (allSelected) {
      // 取消全选
      this.clearSelection();
    } else {
      // 全选
      allTabs.forEach(tab => this.selectedTabs.add(tab.id));
      this.updateSelectionUI();
      this.updateBatchToolbar();
    }
  }

  getAllVisibleTabs() {
    const tabs = [];
    this.windowGroups.forEach(group => {
      tabs.push(...group.tabs);
    });
    return tabs;
  }

  applyCollapsedStates() {
    this.windowGroups.forEach(group => {
      group.isCollapsed = this.collapsedGroups.has(group.id);
    });
  }

  /**
   * 显示搜索结果
   */
  displaySearchResults(results) {
    if (!results || results.length === 0) {
      this.elements.emptyState.style.display = 'flex';
      this.elements.windowGroups.style.display = 'none';
      this.setStatus('没有找到匹配的标签页');
      return;
    }
    
    // 按窗口组织结果
    const groupedResults = this.groupResultsByWindow(results);
    
    // 渲染结果
    const container = this.elements.windowGroups;
    container.innerHTML = '';
    
    this.elements.emptyState.style.display = 'none';
    this.elements.windowGroups.style.display = 'block';
    
    groupedResults.forEach((group, index) => {
      const groupElement = this.createSearchResultGroupElement(group, index);
      container.appendChild(groupElement);
    });
    
    // 启用拖拽功能
    this.enableDragAndDrop();
    
    // 更新统计信息
    this.updateSearchStats(results);
    
    this.setStatus(`找到 ${results.length} 个匹配的标签页`);
  }
  
  /**
   * 按窗口组织搜索结果
   */
  groupResultsByWindow(results) {
    const groupedResults = new Map();
    
    results.forEach(result => {
      const windowId = result.tab.windowId;
      
      if (!groupedResults.has(windowId)) {
        // 查找对应的窗口组信息
        const windowGroup = this.windowGroups.find(g => g.id === windowId);
        if (windowGroup) {
          groupedResults.set(windowId, {
            ...windowGroup,
            tabs: []
          });
        }
      }
      
      const group = groupedResults.get(windowId);
      if (group) {
        group.tabs.push(result.tab);
      }
    });
    
    return Array.from(groupedResults.values());
  }
  
  /**
   * 创建搜索结果组元素
   */
  createSearchResultGroupElement(group, index) {
    const groupDiv = document.createElement('div');
    groupDiv.className = `window-group search-result-group ${group.focused ? 'focused' : ''}`;
    groupDiv.dataset.windowId = group.id;

    const isCollapsed = this.collapsedGroups.has(group.id);
    
    groupDiv.innerHTML = `
      <div class="window-group-header search-result-header" data-window-id="${group.id}">
        <div class="window-group-info">
          <span class="collapse-icon ${isCollapsed ? 'collapsed' : ''}">▼</span>
          <span class="window-group-title">${group.customName || group.title}</span>
          <span class="window-group-count">${group.tabs.length}</span>
          <span class="search-result-badge">搜索结果</span>
          ${group.incognito ? '<span class="incognito-badge">🕶️</span>' : ''}
        </div>
        <div class="window-group-actions">
          <button class="btn btn-icon btn-small" title="重命名窗口组" data-action="rename">
            <span class="icon">✏️</span>
          </button>
          <button class="btn btn-icon btn-small" title="关闭整个窗口" data-action="close-window">
            <span class="icon">✕</span>
          </button>
        </div>
      </div>
      <div class="tab-list search-result-tabs ${isCollapsed ? 'collapsed' : ''}" data-window-id="${group.id}">
        ${group.tabs.map(tab => this.createTabElement(tab)).join('')}
      </div>
    `;

    // 绑定窗口组事件
    this.bindWindowGroupEvents(groupDiv);

    return groupDiv;
  }
  
  /**
   * 更新搜索统计信息
   */
  updateSearchStats(results) {
    const windowCount = new Set(results.map(r => r.tab.windowId)).size;
    const tabCount = results.length;
    const memorySaved = this.calculateMemorySaved(tabCount);

    this.elements.windowCount.textContent = windowCount;
    this.elements.tabCount.textContent = tabCount;
    this.elements.memorySaved.textContent = `${memorySaved}%`;
  }
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 错误处理
   */
  handleError(error, context) {
    console.error(`错误 [${context}]:`, error);
    this.setStatus(`${context}失败`);
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new TabManagerApp();
});