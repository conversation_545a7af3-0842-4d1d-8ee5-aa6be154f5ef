/**
 * 重复标签页检测和处理管理器
 */
class DuplicateTabManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.duplicateGroups = new Map();
    this.duplicateStats = {
      totalDuplicates: 0,
      potentialMemorySaving: 0,
      duplicatesByDomain: {}
    };
  }

  /**
   * 检测所有重复标签页
   */
  detectDuplicates() {
    this.duplicateGroups.clear();
    this.duplicateStats = {
      totalDuplicates: 0,
      potentialMemorySaving: 0,
      duplicatesByDomain: {}
    };

    const urlMap = new Map();
    const allTabs = this.getAllTabs();

    // 按URL分组
    allTabs.forEach(tab => {
      const normalizedUrl = this.normalizeUrl(tab.url);
      if (!urlMap.has(normalizedUrl)) {
        urlMap.set(normalizedUrl, []);
      }
      urlMap.get(normalizedUrl).push(tab);
    });

    // 识别重复组
    urlMap.forEach((tabs, url) => {
      if (tabs.length > 1) {
        this.duplicateGroups.set(url, {
          url: url,
          originalUrl: tabs[0].url,
          tabs: tabs,
          count: tabs.length,
          estimatedMemory: this.estimateTabMemory(tabs[0]) * (tabs.length - 1)
        });

        // 更新统计信息
        this.duplicateStats.totalDuplicates += tabs.length - 1;
        this.duplicateStats.potentialMemorySaving += this.estimateTabMemory(tabs[0]) * (tabs.length - 1);
        
        const domain = this.extractDomain(url);
        if (!this.duplicateStats.duplicatesByDomain[domain]) {
          this.duplicateStats.duplicatesByDomain[domain] = 0;
        }
        this.duplicateStats.duplicatesByDomain[domain] += tabs.length - 1;
      }
    });

    return this.duplicateGroups;
  }

  /**
   * 标准化URL（移除查询参数、片段等）
   */
  normalizeUrl(url) {
    try {
      const urlObj = new URL(url);
      
      // 基础标准化：移除片段
      urlObj.hash = '';
      
      // 可选：移除特定查询参数
      const paramsToRemove = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'fbclid', 'gclid'];
      paramsToRemove.forEach(param => {
        urlObj.searchParams.delete(param);
      });
      
      // 对于特定站点的特殊处理
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        // YouTube视频：保留v参数，移除其他
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          urlObj.search = `?v=${videoId}`;
        }
      } else if (urlObj.hostname.includes('github.com')) {
        // GitHub：保留路径，移除查询参数
        urlObj.search = '';
      }
      
      return urlObj.toString();
    } catch (error) {
      // 如果URL解析失败，返回原始URL
      return url;
    }
  }

  /**
   * 提取域名
   */
  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 估算标签页内存使用
   */
  estimateTabMemory(tab) {
    // 基础内存估算（MB）
    let baseMemory = 50;
    
    // 根据URL类型调整
    if (tab.url.includes('youtube.com') || tab.url.includes('video')) {
      baseMemory = 100; // 视频页面使用更多内存
    } else if (tab.url.includes('gmail.com') || tab.url.includes('docs.google.com')) {
      baseMemory = 80; // 复杂web应用
    } else if (tab.url.includes('github.com')) {
      baseMemory = 60; // 代码页面
    }
    
    // 根据页面状态调整
    if (tab.active) {
      baseMemory *= 1.5; // 活跃标签页使用更多内存
    }
    
    return baseMemory;
  }

  /**
   * 获取所有标签页
   */
  getAllTabs() {
    const allTabs = [];
    this.app.windowGroups.forEach(group => {
      allTabs.push(...group.tabs);
    });
    return allTabs;
  }

  /**
   * 获取重复标签页统计信息
   */
  getDuplicateStats() {
    return {
      ...this.duplicateStats,
      duplicateGroups: this.duplicateGroups.size,
      topDomains: this.getTopDuplicateDomains()
    };
  }

  /**
   * 获取重复最多的域名
   */
  getTopDuplicateDomains() {
    const domains = Object.entries(this.duplicateStats.duplicatesByDomain)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([domain, count]) => ({ domain, count }));
    
    return domains;
  }

  /**
   * 标记重复标签页
   */
  markDuplicateTabs() {
    // 清除现有标记
    document.querySelectorAll('.tab-item').forEach(item => {
      item.classList.remove('has-duplicates');
      const indicator = item.querySelector('.tab-duplicate-indicator');
      if (indicator) {
        indicator.remove();
      }
    });

    // 添加新标记
    this.duplicateGroups.forEach(group => {
      group.tabs.forEach((tab, index) => {
        const tabElement = document.querySelector(`[data-tab-id="${tab.id}"]`);
        if (tabElement) {
          tabElement.classList.add('has-duplicates');
          
          // 添加重复指示器
          const indicator = document.createElement('div');
          indicator.className = 'tab-duplicate-indicator';
          indicator.textContent = group.count;
          indicator.title = `此页面有 ${group.count} 个重复标签页`;
          tabElement.appendChild(indicator);
        }
      });
    });
  }

  /**
   * 关闭重复标签页
   */
  async closeDuplicateTabs(groupUrl, keepActiveTab = true) {
    const group = this.duplicateGroups.get(groupUrl);
    if (!group) return false;

    try {
      let tabsToClose = [...group.tabs];
      
      if (keepActiveTab) {
        // 保留活跃标签页或最新的标签页
        const activeTab = group.tabs.find(tab => tab.active);
        const tabToKeep = activeTab || group.tabs[0];
        tabsToClose = group.tabs.filter(tab => tab.id !== tabToKeep.id);
      }

      if (tabsToClose.length === 0) return false;

      // 批量关闭标签页
      const tabIds = tabsToClose.map(tab => tab.id);
      const success = await this.app.sendMessage('CLOSE_TABS', { tabIds });

      if (success) {
        // 更新统计信息
        this.duplicateStats.totalDuplicates -= tabsToClose.length;
        this.duplicateStats.potentialMemorySaving -= this.estimateTabMemory(group.tabs[0]) * tabsToClose.length;
        
        // 从重复组中移除
        if (keepActiveTab) {
          this.duplicateGroups.delete(groupUrl);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('关闭重复标签页失败:', error);
      return false;
    }
  }

  /**
   * 关闭所有重复标签页
   */
  async closeAllDuplicates(keepActiveTab = true) {
    const results = [];
    
    for (const [groupUrl, group] of this.duplicateGroups) {
      const result = await this.closeDuplicateTabs(groupUrl, keepActiveTab);
      results.push({ groupUrl, success: result, count: group.count - (keepActiveTab ? 1 : 0) });
    }
    
    return results;
  }

  /**
   * 生成重复标签页报告
   */
  generateDuplicateReport() {
    const report = {
      summary: this.getDuplicateStats(),
      details: [],
      recommendations: []
    };

    // 详细信息
    this.duplicateGroups.forEach((group, url) => {
      report.details.push({
        url: url,
        originalUrl: group.originalUrl,
        count: group.count,
        estimatedMemory: group.estimatedMemory,
        tabs: group.tabs.map(tab => ({
          id: tab.id,
          title: tab.title,
          windowId: tab.windowId,
          active: tab.active
        }))
      });
    });

    // 建议
    if (report.summary.totalDuplicates > 0) {
      report.recommendations.push({
        type: 'memory-saving',
        message: `关闭 ${report.summary.totalDuplicates} 个重复标签页可节省约 ${Math.round(report.summary.potentialMemorySaving)}MB 内存`
      });
    }

    if (report.summary.topDomains.length > 0) {
      const topDomain = report.summary.topDomains[0];
      report.recommendations.push({
        type: 'domain-focus',
        message: `${topDomain.domain} 有最多重复标签页 (${topDomain.count}个)，建议优先处理`
      });
    }

    return report;
  }

  /**
   * 创建重复标签页处理界面
   */
  createDuplicatePanel() {
    const panel = document.createElement('div');
    panel.className = 'duplicate-panel';
    panel.innerHTML = `
      <div class="duplicate-panel-header">
        <h3>重复标签页管理</h3>
        <button class="btn btn-icon duplicate-panel-close">✕</button>
      </div>
      <div class="duplicate-stats">
        <div class="stat-item">
          <span class="stat-value">${this.duplicateStats.totalDuplicates}</span>
          <span class="stat-label">重复标签页</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${Math.round(this.duplicateStats.potentialMemorySaving)}MB</span>
          <span class="stat-label">可节省内存</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${this.duplicateGroups.size}</span>
          <span class="stat-label">重复组</span>
        </div>
      </div>
      <div class="duplicate-actions">
        <button class="btn btn-primary" id="closeAllDuplicates">关闭所有重复</button>
        <button class="btn btn-secondary" id="showDuplicateDetails">查看详情</button>
      </div>
      <div class="duplicate-list" id="duplicateList"></div>
    `;

    return panel;
  }

  /**
   * 显示重复标签页详情
   */
  showDuplicateDetails() {
    const listContainer = document.getElementById('duplicateList');
    if (!listContainer) return;

    listContainer.innerHTML = '';
    
    this.duplicateGroups.forEach((group, url) => {
      const groupElement = document.createElement('div');
      groupElement.className = 'duplicate-group';
      groupElement.innerHTML = `
        <div class="duplicate-group-header">
          <div class="duplicate-group-info">
            <div class="duplicate-group-title">${this.truncateText(group.tabs[0].title, 40)}</div>
            <div class="duplicate-group-url">${this.truncateText(url, 50)}</div>
          </div>
          <div class="duplicate-group-actions">
            <span class="duplicate-count">${group.count}个</span>
            <button class="btn btn-small btn-danger" onclick="duplicateManager.closeDuplicateTabs('${url}')">关闭重复</button>
          </div>
        </div>
        <div class="duplicate-tabs">
          ${group.tabs.map(tab => `
            <div class="duplicate-tab-item ${tab.active ? 'active' : ''}">
              <img class="tab-favicon" src="${tab.favIconUrl || ''}" onerror="this.style.display='none'">
              <span class="tab-title">${this.truncateText(tab.title, 30)}</span>
              <span class="tab-window">窗口 ${tab.windowId}</span>
            </div>
          `).join('')}
        </div>
      `;
      
      listContainer.appendChild(groupElement);
    });
  }

  /**
   * 截断文本
   */
  truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  /**
   * 自动检测模式
   */
  enableAutoDetection() {
    // 每30秒检测一次重复标签页
    setInterval(() => {
      if (this.app.windowGroups.length > 0) {
        this.detectDuplicates();
        this.markDuplicateTabs();
      }
    }, 30000);
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.duplicateGroups.clear();
    this.duplicateStats = {
      totalDuplicates: 0,
      potentialMemorySaving: 0,
      duplicatesByDomain: {}
    };
  }
}

export default DuplicateTabManager;