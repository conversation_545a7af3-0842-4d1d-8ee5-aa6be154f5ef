/**
 * 增强数据存储管理器
 * 提供高级数据存储功能：数据同步、备份、版本控制、数据压缩等
 */
class EnhancedStorageManager {
  constructor() {
    this.STORAGE_KEYS = {
      // 核心数据
      WINDOW_GROUPS: 'windowGroups',
      USER_SETTINGS: 'userSettings',
      COLLAPSED_GROUPS: 'collapsedGroups',
      CUSTOM_NAMES: 'customNames',
      SEARCH_HISTORY: 'searchHistory',
      
      // 扩展数据
      TAB_NOTES: 'tabNotes',
      TAB_CATEGORIES: 'tabCategories',
      TAB_TAGS: 'tabTags',
      CUSTOM_GROUPS: 'customGroups',
      STATISTICS_DATA: 'statisticsData',
      IMPORT_EXPORT_SETTINGS: 'importExportSettings',
      
      // 系统数据
      DATA_VERSION: 'dataVersion',
      BACKUP_DATA: 'backupData',
      SYNC_STATUS: 'syncStatus',
      DATA_HASH: 'dataHash',
      PERFORMANCE_METRICS: 'performanceMetrics'
    };
    
    this.CURRENT_VERSION = '2.0.0';
    this.MAX_BACKUP_COUNT = 5;
    this.COMPRESSION_ENABLED = true;
    this.SYNC_INTERVAL = 60000; // 1分钟
    
    this.syncTimer = null;
    this.changeListeners = new Map();
    this.initialized = false;
    
    this.initialize();
  }
  
  /**
   * 初始化存储管理器
   */
  async initialize() {
    if (this.initialized) return;
    
    try {
      // 检查并升级数据版本
      await this.checkAndUpgradeVersion();
      
      // 启动定期同步
      this.startPeriodicSync();
      
      // 监听存储变化
      this.setupStorageListeners();
      
      this.initialized = true;
      console.log('增强存储管理器初始化完成');
    } catch (error) {
      console.error('存储管理器初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 检查并升级数据版本
   */
  async checkAndUpgradeVersion() {
    try {
      const currentVersion = await this.getDataVersion();
      
      if (currentVersion !== this.CURRENT_VERSION) {
        console.log(`数据版本升级: ${currentVersion} -> ${this.CURRENT_VERSION}`);
        
        // 创建升级前备份
        await this.createBackup(`升级前备份 v${currentVersion}`);
        
        // 执行数据迁移
        await this.migrateData(currentVersion, this.CURRENT_VERSION);
        
        // 更新版本号
        await this.setDataVersion(this.CURRENT_VERSION);
      }
    } catch (error) {
      console.error('版本检查失败:', error);
      throw error;
    }
  }
  
  /**
   * 数据迁移
   */
  async migrateData(fromVersion, toVersion) {
    try {
      // 根据版本执行不同的迁移策略
      if (fromVersion === '1.0.0' && toVersion === '2.0.0') {
        await this.migrateFromV1ToV2();
      }
      
      console.log(`数据迁移完成: ${fromVersion} -> ${toVersion}`);
    } catch (error) {
      console.error('数据迁移失败:', error);
      throw error;
    }
  }
  
  /**
   * 从v1.0.0迁移到v2.0.0
   */
  async migrateFromV1ToV2() {
    try {
      // 迁移用户设置
      const oldSettings = await this.loadUserSettings();
      const newSettings = {
        ...oldSettings,
        // 添加新的默认设置
        autoBackup: true,
        syncEnabled: false,
        dataCompression: true,
        performanceTracking: true
      };
      await this.saveUserSettings(newSettings);
      
      // 初始化新的数据结构
      await this.initializeNewDataStructures();
      
      console.log('v1.0.0 -> v2.0.0 迁移完成');
    } catch (error) {
      console.error('v1.0.0 -> v2.0.0 迁移失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化新的数据结构
   */
  async initializeNewDataStructures() {
    try {
      // 初始化统计数据
      const statisticsData = {
        daily: {},
        weekly: {},
        monthly: {},
        domains: {},
        categories: {},
        tags: {},
        usage: {},
        performance: {}
      };
      await this.saveStatisticsData(statisticsData);
      
      // 初始化同步状态
      const syncStatus = {
        enabled: false,
        lastSync: null,
        conflicts: [],
        pendingChanges: []
      };
      await this.saveSyncStatus(syncStatus);
      
      console.log('新数据结构初始化完成');
    } catch (error) {
      console.error('新数据结构初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 压缩数据
   */
  compressData(data) {
    if (!this.COMPRESSION_ENABLED) return data;
    
    try {
      const jsonString = JSON.stringify(data);
      // 简单的压缩算法（实际应用中可以使用更高效的压缩库）
      return btoa(jsonString);
    } catch (error) {
      console.error('数据压缩失败:', error);
      return data;
    }
  }
  
  /**
   * 解压数据
   */
  decompressData(compressedData) {
    if (!this.COMPRESSION_ENABLED) return compressedData;
    
    try {
      if (typeof compressedData === 'string' && compressedData.startsWith('ey')) {
        // 看起来是base64编码的数据
        const jsonString = atob(compressedData);
        return JSON.parse(jsonString);
      }
      return compressedData;
    } catch (error) {
      console.error('数据解压失败:', error);
      return compressedData;
    }
  }
  
  /**
   * 计算数据哈希
   */
  calculateDataHash(data) {
    const jsonString = JSON.stringify(data, Object.keys(data).sort());
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 32位整数
    }
    return hash.toString(16);
  }
  
  /**
   * 保存数据（带压缩和哈希）
   */
  async saveData(key, data, options = {}) {
    try {
      const startTime = performance.now();
      
      // 压缩数据
      const compressedData = options.compress !== false ? this.compressData(data) : data;
      
      // 计算数据哈希
      const dataHash = this.calculateDataHash(data);
      
      // 保存数据
      await chrome.storage.local.set({
        [key]: compressedData,
        [`${key}_hash`]: dataHash,
        [`${key}_timestamp`]: Date.now()
      });
      
      // 记录性能指标
      const endTime = performance.now();
      await this.recordPerformanceMetric('save', key, endTime - startTime);
      
      // 触发变更监听器
      this.triggerChangeListeners(key, data);
      
      return true;
    } catch (error) {
      console.error(`保存数据失败 (${key}):`, error);
      return false;
    }
  }
  
  /**
   * 加载数据（带解压和验证）
   */
  async loadData(key, defaultValue = null) {
    try {
      const startTime = performance.now();
      
      const result = await chrome.storage.local.get([key, `${key}_hash`, `${key}_timestamp`]);
      
      if (!result[key]) {
        return defaultValue;
      }
      
      // 解压数据
      const data = this.decompressData(result[key]);
      
      // 验证数据完整性
      const currentHash = this.calculateDataHash(data);
      const storedHash = result[`${key}_hash`];
      
      if (storedHash && currentHash !== storedHash) {
        console.warn(`数据完整性检查失败 (${key})`);
        // 尝试从备份恢复
        return await this.restoreFromBackup(key, defaultValue);
      }
      
      // 记录性能指标
      const endTime = performance.now();
      await this.recordPerformanceMetric('load', key, endTime - startTime);
      
      return data;
    } catch (error) {
      console.error(`加载数据失败 (${key}):`, error);
      return defaultValue;
    }
  }
  
  /**
   * 创建备份
   */
  async createBackup(description = '') {
    try {
      const timestamp = Date.now();
      const backupId = `backup_${timestamp}`;
      
      // 获取所有数据
      const allData = await chrome.storage.local.get();
      
      // 创建备份对象
      const backup = {
        id: backupId,
        timestamp: timestamp,
        description: description,
        version: this.CURRENT_VERSION,
        data: allData
      };
      
      // 获取现有备份
      const backupData = await this.loadData(this.STORAGE_KEYS.BACKUP_DATA, {});
      
      // 添加新备份
      backupData[backupId] = backup;
      
      // 保持最多5个备份
      const backupIds = Object.keys(backupData).sort((a, b) => {
        return backupData[b].timestamp - backupData[a].timestamp;
      });
      
      if (backupIds.length > this.MAX_BACKUP_COUNT) {
        const toDelete = backupIds.slice(this.MAX_BACKUP_COUNT);
        toDelete.forEach(id => delete backupData[id]);
      }
      
      // 保存备份数据
      await this.saveData(this.STORAGE_KEYS.BACKUP_DATA, backupData);
      
      console.log(`备份创建成功: ${backupId}`);
      return backupId;
    } catch (error) {
      console.error('创建备份失败:', error);
      throw error;
    }
  }
  
  /**
   * 恢复备份
   */
  async restoreBackup(backupId) {
    try {
      const backupData = await this.loadData(this.STORAGE_KEYS.BACKUP_DATA, {});
      const backup = backupData[backupId];
      
      if (!backup) {
        throw new Error(`备份不存在: ${backupId}`);
      }
      
      // 清除当前数据
      await chrome.storage.local.clear();
      
      // 恢复备份数据
      await chrome.storage.local.set(backup.data);
      
      console.log(`备份恢复成功: ${backupId}`);
      return true;
    } catch (error) {
      console.error('恢复备份失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取备份列表
   */
  async getBackupList() {
    try {
      const backupData = await this.loadData(this.STORAGE_KEYS.BACKUP_DATA, {});
      
      return Object.values(backupData).sort((a, b) => {
        return b.timestamp - a.timestamp;
      });
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return [];
    }
  }
  
  /**
   * 删除备份
   */
  async deleteBackup(backupId) {
    try {
      const backupData = await this.loadData(this.STORAGE_KEYS.BACKUP_DATA, {});
      
      if (backupData[backupId]) {
        delete backupData[backupId];
        await this.saveData(this.STORAGE_KEYS.BACKUP_DATA, backupData);
        console.log(`备份删除成功: ${backupId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('删除备份失败:', error);
      return false;
    }
  }
  
  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(key, defaultValue) {
    try {
      const backupList = await this.getBackupList();
      
      for (const backup of backupList) {
        if (backup.data[key]) {
          console.log(`从备份恢复数据: ${key}`);
          return this.decompressData(backup.data[key]);
        }
      }
      
      return defaultValue;
    } catch (error) {
      console.error('从备份恢复数据失败:', error);
      return defaultValue;
    }
  }
  
  /**
   * 记录性能指标
   */
  async recordPerformanceMetric(operation, key, duration) {
    try {
      const metrics = await this.loadData(this.STORAGE_KEYS.PERFORMANCE_METRICS, {});
      
      if (!metrics[operation]) {
        metrics[operation] = {};
      }
      
      if (!metrics[operation][key]) {
        metrics[operation][key] = {
          count: 0,
          totalTime: 0,
          averageTime: 0,
          maxTime: 0,
          minTime: Infinity
        };
      }
      
      const metric = metrics[operation][key];
      metric.count++;
      metric.totalTime += duration;
      metric.averageTime = metric.totalTime / metric.count;
      metric.maxTime = Math.max(metric.maxTime, duration);
      metric.minTime = Math.min(metric.minTime, duration);
      
      await this.saveData(this.STORAGE_KEYS.PERFORMANCE_METRICS, metrics);
    } catch (error) {
      console.error('记录性能指标失败:', error);
    }
  }
  
  /**
   * 获取性能指标
   */
  async getPerformanceMetrics() {
    try {
      return await this.loadData(this.STORAGE_KEYS.PERFORMANCE_METRICS, {});
    } catch (error) {
      console.error('获取性能指标失败:', error);
      return {};
    }
  }
  
  /**
   * 启动定期同步
   */
  startPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    this.syncTimer = setInterval(async () => {
      try {
        const syncStatus = await this.loadSyncStatus();
        if (syncStatus.enabled) {
          await this.syncData();
        }
      } catch (error) {
        console.error('定期同步失败:', error);
      }
    }, this.SYNC_INTERVAL);
  }
  
  /**
   * 停止定期同步
   */
  stopPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }
  
  /**
   * 同步数据
   */
  async syncData() {
    try {
      const syncStatus = await this.loadSyncStatus();
      
      if (!syncStatus.enabled) {
        return;
      }
      
      // 检查是否有待同步的更改
      if (syncStatus.pendingChanges.length === 0) {
        return;
      }
      
      // 这里可以实现与云端的同步逻辑
      console.log('开始同步数据...');
      
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新同步状态
      syncStatus.lastSync = Date.now();
      syncStatus.pendingChanges = [];
      
      await this.saveSyncStatus(syncStatus);
      
      console.log('数据同步完成');
    } catch (error) {
      console.error('数据同步失败:', error);
    }
  }
  
  /**
   * 设置存储变更监听器
   */
  setupStorageListeners() {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local') {
        Object.keys(changes).forEach(key => {
          if (this.changeListeners.has(key)) {
            const listeners = this.changeListeners.get(key);
            listeners.forEach(listener => {
              try {
                listener(changes[key].newValue, changes[key].oldValue);
              } catch (error) {
                console.error('存储变更监听器执行失败:', error);
              }
            });
          }
        });
      }
    });
  }
  
  /**
   * 添加变更监听器
   */
  addChangeListener(key, listener) {
    if (!this.changeListeners.has(key)) {
      this.changeListeners.set(key, new Set());
    }
    this.changeListeners.get(key).add(listener);
  }
  
  /**
   * 移除变更监听器
   */
  removeChangeListener(key, listener) {
    if (this.changeListeners.has(key)) {
      this.changeListeners.get(key).delete(listener);
    }
  }
  
  /**
   * 触发变更监听器
   */
  triggerChangeListeners(key, newValue, oldValue = null) {
    if (this.changeListeners.has(key)) {
      const listeners = this.changeListeners.get(key);
      listeners.forEach(listener => {
        try {
          listener(newValue, oldValue);
        } catch (error) {
          console.error('变更监听器执行失败:', error);
        }
      });
    }
  }
  
  // 数据版本管理
  async getDataVersion() {
    return await this.loadData(this.STORAGE_KEYS.DATA_VERSION, '1.0.0');
  }
  
  async setDataVersion(version) {
    return await this.saveData(this.STORAGE_KEYS.DATA_VERSION, version);
  }
  
  // 同步状态管理
  async loadSyncStatus() {
    return await this.loadData(this.STORAGE_KEYS.SYNC_STATUS, {
      enabled: false,
      lastSync: null,
      conflicts: [],
      pendingChanges: []
    });
  }
  
  async saveSyncStatus(syncStatus) {
    return await this.saveData(this.STORAGE_KEYS.SYNC_STATUS, syncStatus);
  }
  
  // 统计数据管理
  async loadStatisticsData() {
    return await this.loadData(this.STORAGE_KEYS.STATISTICS_DATA, {
      daily: {},
      weekly: {},
      monthly: {},
      domains: {},
      categories: {},
      tags: {},
      usage: {},
      performance: {}
    });
  }
  
  async saveStatisticsData(statisticsData) {
    return await this.saveData(this.STORAGE_KEYS.STATISTICS_DATA, statisticsData);
  }
  
  // 原有方法的增强版本
  async saveWindowGroups(windowGroups) {
    return await this.saveData(this.STORAGE_KEYS.WINDOW_GROUPS, windowGroups);
  }
  
  async loadWindowGroups() {
    return await this.loadData(this.STORAGE_KEYS.WINDOW_GROUPS, []);
  }
  
  async saveUserSettings(settings) {
    return await this.saveData(this.STORAGE_KEYS.USER_SETTINGS, settings);
  }
  
  async loadUserSettings() {
    return await this.loadData(this.STORAGE_KEYS.USER_SETTINGS, this.getDefaultSettings());
  }
  
  async saveCollapsedGroups(collapsedGroups) {
    return await this.saveData(this.STORAGE_KEYS.COLLAPSED_GROUPS, collapsedGroups);
  }
  
  async loadCollapsedGroups() {
    return await this.loadData(this.STORAGE_KEYS.COLLAPSED_GROUPS, {});
  }
  
  async saveCustomNames(customNames) {
    return await this.saveData(this.STORAGE_KEYS.CUSTOM_NAMES, customNames);
  }
  
  async loadCustomNames() {
    return await this.loadData(this.STORAGE_KEYS.CUSTOM_NAMES, {});
  }
  
  async saveSearchHistory(searchHistory) {
    return await this.saveData(this.STORAGE_KEYS.SEARCH_HISTORY, searchHistory);
  }
  
  async loadSearchHistory() {
    return await this.loadData(this.STORAGE_KEYS.SEARCH_HISTORY, []);
  }
  
  // 新增的数据管理方法
  async saveTabNotes(tabNotes) {
    return await this.saveData(this.STORAGE_KEYS.TAB_NOTES, tabNotes);
  }
  
  async loadTabNotes() {
    return await this.loadData(this.STORAGE_KEYS.TAB_NOTES, {});
  }
  
  async saveTabCategories(tabCategories) {
    return await this.saveData(this.STORAGE_KEYS.TAB_CATEGORIES, tabCategories);
  }
  
  async loadTabCategories() {
    return await this.loadData(this.STORAGE_KEYS.TAB_CATEGORIES, {});
  }
  
  async saveTabTags(tabTags) {
    return await this.saveData(this.STORAGE_KEYS.TAB_TAGS, tabTags);
  }
  
  async loadTabTags() {
    return await this.loadData(this.STORAGE_KEYS.TAB_TAGS, {});
  }
  
  async saveCustomGroups(customGroups) {
    return await this.saveData(this.STORAGE_KEYS.CUSTOM_GROUPS, customGroups);
  }
  
  async loadCustomGroups() {
    return await this.loadData(this.STORAGE_KEYS.CUSTOM_GROUPS, {});
  }
  
  async saveImportExportSettings(settings) {
    return await this.saveData(this.STORAGE_KEYS.IMPORT_EXPORT_SETTINGS, settings);
  }
  
  async loadImportExportSettings() {
    return await this.loadData(this.STORAGE_KEYS.IMPORT_EXPORT_SETTINGS, {});
  }
  
  /**
   * 获取默认设置
   */
  getDefaultSettings() {
    return {
      // 原有设置
      autoCollapse: false,
      showFavicons: true,
      showTabCount: true,
      sortOrder: 'recent',
      theme: 'light',
      compactMode: false,
      
      // 新增设置
      autoBackup: true,
      syncEnabled: false,
      dataCompression: true,
      performanceTracking: true,
      maxBackupCount: 5,
      syncInterval: 60000,
      
      // 通知设置
      showNotifications: true,
      notificationDuration: 3000,
      
      // 隐私设置
      dataCollection: true,
      anonymousAnalytics: false,
      
      // 高级设置
      debugMode: false,
      experimentalFeatures: false
    };
  }
  
  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      
      return {
        used: usage,
        quota: quota,
        percentage: (usage / quota) * 100,
        available: quota - usage,
        formatted: {
          used: this.formatBytes(usage),
          quota: this.formatBytes(quota),
          available: this.formatBytes(quota - usage)
        }
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return {
        used: 0,
        quota: 0,
        percentage: 0,
        available: 0,
        formatted: {
          used: '0 B',
          quota: '0 B',
          available: '0 B'
        }
      };
    }
  }
  
  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * 清理所有数据
   */
  async clearAllData() {
    try {
      // 创建清理前备份
      await this.createBackup('清理前备份');
      
      // 清理存储
      await chrome.storage.local.clear();
      
      // 重新初始化
      await this.initialize();
      
      return true;
    } catch (error) {
      console.error('清理数据失败:', error);
      return false;
    }
  }
  
  /**
   * 导出所有数据
   */
  async exportAllData() {
    try {
      const allData = await chrome.storage.local.get();
      
      const exportData = {
        timestamp: Date.now(),
        version: this.CURRENT_VERSION,
        data: allData
      };
      
      return exportData;
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 导入数据
   */
  async importData(importData) {
    try {
      // 验证数据格式
      if (!importData.data || !importData.version) {
        throw new Error('无效的导入数据格式');
      }
      
      // 创建导入前备份
      await this.createBackup('导入前备份');
      
      // 清理现有数据
      await chrome.storage.local.clear();
      
      // 导入数据
      await chrome.storage.local.set(importData.data);
      
      // 检查版本并升级
      await this.checkAndUpgradeVersion();
      
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    this.stopPeriodicSync();
    this.changeListeners.clear();
    this.initialized = false;
  }
}

export default EnhancedStorageManager;