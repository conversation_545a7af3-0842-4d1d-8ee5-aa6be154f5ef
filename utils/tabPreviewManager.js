/**
 * 标签页预览管理器
 * 提供丰富的标签页预览功能，包括截图、元数据、统计信息等
 */
class TabPreviewManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.previewCache = new Map();
    this.previewTimeout = null;
    this.currentPreview = null;
    this.previewDelay = 800; // 800ms 延迟显示
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存过期
    
    this.initializePreview();
  }

  /**
   * 初始化预览功能
   */
  initializePreview() {
    this.createPreviewContainer();
    this.bindPreviewEvents();
    this.setupKeyboardShortcuts();
  }

  /**
   * 创建预览容器
   */
  createPreviewContainer() {
    const previewContainer = document.createElement('div');
    previewContainer.className = 'tab-preview-container';
    previewContainer.innerHTML = `
      <div class="tab-preview-content">
        <div class="preview-header">
          <div class="preview-title">标签页预览</div>
          <div class="preview-actions">
            <button class="preview-action-btn" data-action="mode" title="全屏预览">🔳</button>
            <button class="preview-action-btn" data-action="pin" title="固定预览">📌</button>
            <button class="preview-action-btn" data-action="close" title="关闭预览">✕</button>
          </div>
        </div>
        
        <div class="preview-body">
          <div class="preview-screenshot">
            <div class="screenshot-placeholder">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载预览...</div>
            </div>
          </div>
          
          <div class="preview-info">
            <div class="tab-basic-info">
              <div class="tab-favicon-large">
                <img class="favicon-img" src="" alt="">
                <div class="favicon-placeholder">🌐</div>
              </div>
              <div class="tab-title-large"></div>
              <div class="tab-url-large"></div>
            </div>
            
            <div class="tab-metadata-section">
              <div class="metadata-item">
                <span class="metadata-label">状态:</span>
                <span class="metadata-value status-value"></span>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">分类:</span>
                <span class="metadata-value category-value"></span>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">标签:</span>
                <div class="metadata-value tags-value"></div>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">优先级:</span>
                <span class="metadata-value priority-value"></span>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">评分:</span>
                <span class="metadata-value rating-value"></span>
              </div>
            </div>
            
            <div class="tab-notes-section">
              <div class="notes-label">备注:</div>
              <div class="notes-content"></div>
            </div>
            
            <div class="tab-stats-section">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-label">访问次数</div>
                  <div class="stat-value visit-count">-</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">停留时间</div>
                  <div class="stat-value stay-time">-</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">内存使用</div>
                  <div class="stat-value memory-usage">-</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">最后访问</div>
                  <div class="stat-value last-visit">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="preview-footer">
          <div class="preview-tabs">
            <button class="preview-tab active" data-tab="overview">概览</button>
            <button class="preview-tab" data-tab="history">历史</button>
            <button class="preview-tab" data-tab="related">相关</button>
          </div>
          
          <div class="preview-tab-content active" data-content="overview">
            <div class="overview-content">
              <div class="quick-actions">
                <button class="quick-action-btn" data-action="duplicate">复制标签页</button>
                <button class="quick-action-btn" data-action="bookmark">添加书签</button>
                <button class="quick-action-btn" data-action="move">移动窗口</button>
                <button class="quick-action-btn" data-action="close">关闭标签页</button>
              </div>
            </div>
          </div>
          
          <div class="preview-tab-content" data-content="history">
            <div class="history-content">
              <div class="visit-history-list"></div>
            </div>
          </div>
          
          <div class="preview-tab-content" data-content="related">
            <div class="related-content">
              <div class="related-tabs-list"></div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    previewContainer.style.display = 'none';
    document.body.appendChild(previewContainer);
    
    this.previewContainer = previewContainer;
    this.bindPreviewContainerEvents();
  }

  /**
   * 绑定预览容器事件
   */
  bindPreviewContainerEvents() {
    // 预览操作按钮
    this.previewContainer.querySelectorAll('.preview-action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        this.handlePreviewAction(action);
      });
    });

    // 预览标签页切换
    this.previewContainer.querySelectorAll('.preview-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const tabName = e.target.dataset.tab;
        this.switchPreviewTab(tabName);
      });
    });

    // 快速操作按钮
    this.previewContainer.querySelectorAll('.quick-action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        this.handleQuickAction(action);
      });
    });

    // 点击外部关闭
    this.previewContainer.addEventListener('click', (e) => {
      if (e.target === this.previewContainer) {
        this.hidePreview();
      }
    });
  }

  /**
   * 绑定预览事件
   */
  bindPreviewEvents() {
    // 鼠标悬停显示预览
    document.addEventListener('mouseover', (e) => {
      const tabItem = e.target.closest('.tab-item');
      if (tabItem) {
        const tabId = parseInt(tabItem.dataset.tabId);
        this.schedulePreview(tabId, e.clientX, e.clientY);
      }
    });

    // 鼠标离开隐藏预览
    document.addEventListener('mouseout', (e) => {
      const tabItem = e.target.closest('.tab-item');
      if (tabItem) {
        this.cancelPreview();
      }
    });

    // 滚动时隐藏预览
    document.addEventListener('scroll', () => {
      if (this.currentPreview && !this.isPinned()) {
        this.hidePreview();
      }
    });
  }

  /**
   * 设置键盘快捷键
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (this.currentPreview) {
          const isFullscreen = this.previewContainer.classList.contains('fullscreen-mode');
          if (isFullscreen) {
            this.exitFullscreenMode();
          } else {
            this.hidePreview();
          }
        }
      }
      
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        this.togglePreviewMode();
      }
    });
  }

  /**
   * 安排预览显示
   */
  schedulePreview(tabId, x, y) {
    this.cancelPreview();
    
    this.previewTimeout = setTimeout(() => {
      this.showPreview(tabId, x, y);
    }, this.previewDelay);
  }

  /**
   * 取消预览
   */
  cancelPreview() {
    if (this.previewTimeout) {
      clearTimeout(this.previewTimeout);
      this.previewTimeout = null;
    }
    
    if (this.currentPreview && !this.isPinned()) {
      this.hidePreview();
    }
  }

  /**
   * 显示预览
   */
  async showPreview(tabId, x, y) {
    try {
      const tabData = await this.getTabData(tabId);
      if (!tabData) return;

      this.currentPreview = {
        tabId,
        tabData,
        x,
        y,
        pinned: false
      };

      await this.populatePreview(tabData);
      this.positionPreview(x, y);
      this.previewContainer.style.display = 'block';
      this.previewContainer.classList.add('visible');

      // 异步加载截图
      this.loadTabScreenshot(tabId);
      
      // 加载扩展数据
      await this.loadExtendedData(tabId);
      
    } catch (error) {
      console.error('显示预览失败:', error);
    }
  }

  /**
   * 隐藏预览
   */
  hidePreview() {
    if (this.currentPreview && this.isPinned()) {
      return; // 固定的预览不自动隐藏
    }

    this.previewContainer.style.display = 'none';
    this.previewContainer.classList.remove('visible');
    this.currentPreview = null;
  }

  /**
   * 获取标签页数据
   */
  async getTabData(tabId) {
    try {
      // 查找标签页
      const tab = this.findTabById(tabId);
      if (!tab) return null;

      // 获取元数据
      const metadata = this.app.tabNotesManager.getTabMetadata(tabId);
      
      // 获取统计信息
      const stats = await this.getTabStats(tabId);
      
      return {
        tab,
        metadata,
        stats
      };
    } catch (error) {
      console.error('获取标签页数据失败:', error);
      return null;
    }
  }

  /**
   * 查找标签页
   */
  findTabById(tabId) {
    for (const group of this.app.windowGroups) {
      const tab = group.tabs.find(t => t.id === tabId);
      if (tab) return tab;
    }
    return null;
  }

  /**
   * 获取标签页统计信息
   */
  async getTabStats(tabId) {
    try {
      // 从存储中获取统计数据
      const statsData = await chrome.storage.local.get(['tabStats']);
      const tabStats = statsData.tabStats || {};
      
      const stats = tabStats[tabId] || {
        visitCount: 0,
        totalTime: 0,
        lastVisit: null,
        memoryUsage: 0
      };

      return {
        visitCount: stats.visitCount,
        totalTime: stats.totalTime,
        lastVisit: stats.lastVisit,
        memoryUsage: stats.memoryUsage,
        avgSessionTime: stats.visitCount > 0 ? stats.totalTime / stats.visitCount : 0
      };
    } catch (error) {
      console.error('获取标签页统计失败:', error);
      return {
        visitCount: 0,
        totalTime: 0,
        lastVisit: null,
        memoryUsage: 0,
        avgSessionTime: 0
      };
    }
  }

  /**
   * 填充预览内容
   */
  async populatePreview(tabData) {
    const { tab, metadata, stats } = tabData;
    
    // 基本信息
    const faviconImg = this.previewContainer.querySelector('.favicon-img');
    const faviconPlaceholder = this.previewContainer.querySelector('.favicon-placeholder');
    
    if (tab.favIconUrl) {
      faviconImg.src = tab.favIconUrl;
      faviconImg.style.display = 'block';
      faviconPlaceholder.style.display = 'none';
    } else {
      faviconImg.style.display = 'none';
      faviconPlaceholder.style.display = 'block';
    }

    this.previewContainer.querySelector('.tab-title-large').textContent = tab.title;
    this.previewContainer.querySelector('.tab-url-large').textContent = tab.url;

    // 状态
    const statusValue = this.previewContainer.querySelector('.status-value');
    statusValue.textContent = tab.active ? '活跃' : '后台';
    statusValue.className = `metadata-value status-value ${tab.active ? 'active' : 'inactive'}`;

    // 分类
    const categoryValue = this.previewContainer.querySelector('.category-value');
    if (metadata.category) {
      const category = this.app.tabNotesManager.categories.get(metadata.category);
      categoryValue.innerHTML = category ? `${category.icon} ${category.name}` : metadata.category;
    } else {
      categoryValue.textContent = '未分类';
    }

    // 标签
    const tagsValue = this.previewContainer.querySelector('.tags-value');
    if (metadata.tags && metadata.tags.length > 0) {
      tagsValue.innerHTML = metadata.tags.map(tag => 
        `<span class="preview-tag">${tag}</span>`
      ).join('');
    } else {
      tagsValue.textContent = '无标签';
    }

    // 优先级
    const priorityValue = this.previewContainer.querySelector('.priority-value');
    if (metadata.priority > 3) {
      priorityValue.innerHTML = '⭐'.repeat(metadata.priority - 3) + ` (${metadata.priority})`;
    } else {
      priorityValue.textContent = `普通 (${metadata.priority})`;
    }

    // 评分
    const ratingValue = this.previewContainer.querySelector('.rating-value');
    if (metadata.rating > 0) {
      ratingValue.innerHTML = '⭐'.repeat(metadata.rating) + ` (${metadata.rating}/5)`;
    } else {
      ratingValue.textContent = '未评分';
    }

    // 备注
    const notesContent = this.previewContainer.querySelector('.notes-content');
    notesContent.textContent = metadata.notes || '无备注';

    // 统计信息
    this.previewContainer.querySelector('.visit-count').textContent = stats.visitCount;
    this.previewContainer.querySelector('.stay-time').textContent = this.formatTime(stats.totalTime);
    this.previewContainer.querySelector('.memory-usage').textContent = this.formatMemory(stats.memoryUsage);
    this.previewContainer.querySelector('.last-visit').textContent = stats.lastVisit ? 
      new Date(stats.lastVisit).toLocaleString() : '从未访问';
  }

  /**
   * 加载标签页截图
   */
  async loadTabScreenshot(tabId) {
    try {
      const cachedScreenshot = this.previewCache.get(`screenshot-${tabId}`);
      if (cachedScreenshot && Date.now() - cachedScreenshot.timestamp < this.cacheExpiry) {
        this.displayScreenshot(cachedScreenshot.data);
        return;
      }

      // 尝试获取标签页截图
      const screenshot = await this.captureTabScreenshot(tabId);
      if (screenshot) {
        this.previewCache.set(`screenshot-${tabId}`, {
          data: screenshot,
          timestamp: Date.now()
        });
        this.displayScreenshot(screenshot);
      } else {
        this.displayScreenshotPlaceholder();
      }
    } catch (error) {
      console.error('加载截图失败:', error);
      this.displayScreenshotPlaceholder();
    }
  }

  /**
   * 捕获标签页截图
   */
  async captureTabScreenshot(tabId) {
    try {
      // 使用 chrome.tabs.captureVisibleTab API
      const tab = this.findTabById(tabId);
      if (!tab) return null;

      const screenshot = await chrome.tabs.captureVisibleTab(tab.windowId, {
        format: 'jpeg',
        quality: 80
      });

      return screenshot;
    } catch (error) {
      console.error('捕获截图失败:', error);
      return null;
    }
  }

  /**
   * 显示截图
   */
  displayScreenshot(screenshot) {
    const screenshotContainer = this.previewContainer.querySelector('.preview-screenshot');
    screenshotContainer.innerHTML = `
      <img src="${screenshot}" alt="标签页截图" class="screenshot-image">
    `;
  }

  /**
   * 显示截图占位符
   */
  displayScreenshotPlaceholder() {
    const screenshotContainer = this.previewContainer.querySelector('.preview-screenshot');
    screenshotContainer.innerHTML = `
      <div class="screenshot-placeholder">
        <div class="placeholder-icon">🖼️</div>
        <div class="placeholder-text">无法获取预览图</div>
      </div>
    `;
  }

  /**
   * 加载扩展数据
   */
  async loadExtendedData(tabId) {
    try {
      // 加载访问历史
      const history = await this.getTabHistory(tabId);
      this.populateHistory(history);

      // 加载相关标签页
      const related = await this.getRelatedTabs(tabId);
      this.populateRelated(related);
    } catch (error) {
      console.error('加载扩展数据失败:', error);
    }
  }

  /**
   * 获取标签页历史
   */
  async getTabHistory(tabId) {
    try {
      const historyData = await chrome.storage.local.get(['tabHistory']);
      const tabHistory = historyData.tabHistory || {};
      
      return tabHistory[tabId] || [];
    } catch (error) {
      console.error('获取历史失败:', error);
      return [];
    }
  }

  /**
   * 获取相关标签页
   */
  async getRelatedTabs(tabId) {
    try {
      const tab = this.findTabById(tabId);
      if (!tab) return [];

      const domain = new URL(tab.url).hostname;
      const related = [];

      // 查找相同域名的标签页
      for (const group of this.app.windowGroups) {
        for (const t of group.tabs) {
          if (t.id !== tabId) {
            try {
              const tabDomain = new URL(t.url).hostname;
              if (tabDomain === domain) {
                related.push(t);
              }
            } catch (e) {
              // 忽略无效URL
            }
          }
        }
      }

      return related.slice(0, 10); // 限制10个相关标签页
    } catch (error) {
      console.error('获取相关标签页失败:', error);
      return [];
    }
  }

  /**
   * 填充历史内容
   */
  populateHistory(history) {
    const historyList = this.previewContainer.querySelector('.visit-history-list');
    
    if (history.length === 0) {
      historyList.innerHTML = '<div class="empty-history">暂无访问历史</div>';
      return;
    }

    historyList.innerHTML = history.slice(0, 10).map(item => `
      <div class="history-item">
        <div class="history-time">${new Date(item.timestamp).toLocaleString()}</div>
        <div class="history-duration">${this.formatTime(item.duration)}</div>
      </div>
    `).join('');
  }

  /**
   * 填充相关内容
   */
  populateRelated(related) {
    const relatedList = this.previewContainer.querySelector('.related-tabs-list');
    
    if (related.length === 0) {
      relatedList.innerHTML = '<div class="empty-related">暂无相关标签页</div>';
      return;
    }

    relatedList.innerHTML = related.map(tab => `
      <div class="related-tab-item" data-tab-id="${tab.id}">
        <img src="${tab.favIconUrl || ''}" class="related-favicon" onerror="this.style.display='none'">
        <div class="related-info">
          <div class="related-title">${tab.title}</div>
          <div class="related-url">${tab.url}</div>
        </div>
      </div>
    `).join('');

    // 绑定相关标签页点击事件
    relatedList.querySelectorAll('.related-tab-item').forEach(item => {
      item.addEventListener('click', (e) => {
        const tabId = parseInt(e.currentTarget.dataset.tabId);
        this.app.activateTab(tabId);
      });
    });
  }

  /**
   * 定位预览
   */
  positionPreview(x, y) {
    const preview = this.previewContainer;
    const rect = preview.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = x + 10;
    let top = y + 10;

    // 防止超出视口
    if (left + rect.width > viewportWidth) {
      left = x - rect.width - 10;
    }
    if (top + rect.height > viewportHeight) {
      top = y - rect.height - 10;
    }

    preview.style.left = `${Math.max(0, left)}px`;
    preview.style.top = `${Math.max(0, top)}px`;
  }

  /**
   * 处理预览操作
   */
  handlePreviewAction(action) {
    switch (action) {
      case 'mode':
        this.togglePreviewMode();
        break;
      case 'pin':
        this.togglePin();
        break;
      case 'close':
        this.hidePreview();
        break;
    }
  }

  /**
   * 切换预览标签页
   */
  switchPreviewTab(tabName) {
    // 更新标签页状态
    this.previewContainer.querySelectorAll('.preview-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    this.previewContainer.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 更新内容
    this.previewContainer.querySelectorAll('.preview-tab-content').forEach(content => {
      content.classList.remove('active');
    });
    this.previewContainer.querySelector(`[data-content="${tabName}"]`).classList.add('active');
  }

  /**
   * 处理快速操作
   */
  async handleQuickAction(action) {
    if (!this.currentPreview) return;

    const tabId = this.currentPreview.tabId;
    
    try {
      switch (action) {
        case 'duplicate':
          await this.duplicateTab(tabId);
          break;
        case 'bookmark':
          await this.bookmarkTab(tabId);
          break;
        case 'move':
          await this.moveTab(tabId);
          break;
        case 'close':
          await this.closeTab(tabId);
          break;
      }
    } catch (error) {
      console.error('快速操作失败:', error);
    }
  }

  /**
   * 复制标签页
   */
  async duplicateTab(tabId) {
    const tab = this.findTabById(tabId);
    if (!tab) return;

    const success = await this.app.sendMessage('OPEN_TAB_CURRENT_WINDOW', {
      url: tab.url,
      windowId: tab.windowId
    });

    if (success) {
      this.app.setStatus('标签页已复制');
      await this.app.refreshData();
    }
  }

  /**
   * 添加书签
   */
  async bookmarkTab(tabId) {
    await this.app.tabNotesManager.toggleTabBookmark(tabId);
    this.app.setStatus('书签状态已更新');
    await this.app.refreshData();
  }

  /**
   * 移动标签页
   */
  async moveTab(tabId) {
    try {
      const tab = this.findTabById(tabId);
      if (!tab) {
        this.app.setStatus('标签页不存在');
        return;
      }

      // 创建窗口选择器对话框
      const windowSelectorDialog = this.createWindowSelectorDialog(tabId);
      document.body.appendChild(windowSelectorDialog);
      
    } catch (error) {
      console.error('移动标签页失败:', error);
      this.app.setStatus('移动标签页失败');
    }
  }

  /**
   * 创建窗口选择器对话框
   */
  createWindowSelectorDialog(tabId) {
    const dialog = document.createElement('div');
    dialog.className = 'window-selector-dialog';
    dialog.innerHTML = `
      <div class="dialog-backdrop"></div>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>移动标签页到窗口</h3>
          <button class="close-dialog">✕</button>
        </div>
        <div class="dialog-body">
          <div class="window-list">
            ${this.app.windowGroups.map(group => `
              <div class="window-option" data-window-id="${group.id}">
                <div class="window-info">
                  <div class="window-title">${group.customName || group.title}</div>
                  <div class="window-details">${group.tabs.length} 个标签页</div>
                </div>
                <div class="window-actions">
                  <button class="btn btn-primary move-to-window" data-window-id="${group.id}">
                    移动到此窗口
                  </button>
                </div>
              </div>
            `).join('')}
            <div class="window-option new-window">
              <div class="window-info">
                <div class="window-title">新窗口</div>
                <div class="window-details">创建新的浏览器窗口</div>
              </div>
              <div class="window-actions">
                <button class="btn btn-primary move-to-new-window">
                  移动到新窗口
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="btn btn-secondary cancel-move">取消</button>
        </div>
      </div>
    `;

    // 绑定事件
    const closeDialog = () => {
      dialog.remove();
    };

    dialog.querySelector('.close-dialog').addEventListener('click', closeDialog);
    dialog.querySelector('.cancel-move').addEventListener('click', closeDialog);
    dialog.querySelector('.dialog-backdrop').addEventListener('click', closeDialog);

    // 移动到现有窗口
    dialog.querySelectorAll('.move-to-window').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const targetWindowId = parseInt(e.target.dataset.windowId);
        await this.moveTabToWindow(tabId, targetWindowId);
        closeDialog();
      });
    });

    // 移动到新窗口
    dialog.querySelector('.move-to-new-window').addEventListener('click', async () => {
      await this.moveTabToNewWindow(tabId);
      closeDialog();
    });

    return dialog;
  }

  /**
   * 移动标签页到指定窗口
   */
  async moveTabToWindow(tabId, targetWindowId) {
    try {
      const success = await this.app.sendMessage('MOVE_TAB_TO_WINDOW', {
        tabId,
        targetWindowId
      });

      if (success) {
        this.app.setStatus('标签页已移动');
        this.hidePreview();
        await this.app.refreshData();
      } else {
        this.app.setStatus('移动标签页失败');
      }
    } catch (error) {
      console.error('移动标签页到窗口失败:', error);
      this.app.setStatus('移动标签页失败');
    }
  }

  /**
   * 移动标签页到新窗口
   */
  async moveTabToNewWindow(tabId) {
    try {
      const success = await this.app.sendMessage('MOVE_TAB_TO_NEW_WINDOW', {
        tabId
      });

      if (success) {
        this.app.setStatus('标签页已移动到新窗口');
        this.hidePreview();
        await this.app.refreshData();
      } else {
        this.app.setStatus('移动标签页失败');
      }
    } catch (error) {
      console.error('移动标签页到新窗口失败:', error);
      this.app.setStatus('移动标签页失败');
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId) {
    const success = await this.app.sendMessage('CLOSE_TAB', { tabId });
    if (success) {
      this.app.setStatus('标签页已关闭');
      this.hidePreview();
      await this.app.refreshData();
    }
  }

  /**
   * 切换固定状态
   */
  togglePin() {
    if (!this.currentPreview) return;

    this.currentPreview.pinned = !this.currentPreview.pinned;
    const pinBtn = this.previewContainer.querySelector('[data-action="pin"]');
    
    if (this.currentPreview.pinned) {
      pinBtn.textContent = '📍';
      pinBtn.title = '取消固定';
      this.previewContainer.classList.add('pinned');
    } else {
      pinBtn.textContent = '📌';
      pinBtn.title = '固定预览';
      this.previewContainer.classList.remove('pinned');
    }
  }

  /**
   * 检查是否固定
   */
  isPinned() {
    return this.currentPreview && this.currentPreview.pinned;
  }

  /**
   * 切换预览模式
   */
  togglePreviewMode() {
    if (!this.currentPreview) {
      this.app.setStatus('请先打开标签页预览');
      return;
    }

    const isFullscreen = this.previewContainer.classList.contains('fullscreen-mode');
    
    if (isFullscreen) {
      this.exitFullscreenMode();
    } else {
      this.enterFullscreenMode();
    }
  }

  /**
   * 进入全屏预览模式
   */
  enterFullscreenMode() {
    this.previewContainer.classList.add('fullscreen-mode');
    this.previewContainer.style.position = 'fixed';
    this.previewContainer.style.top = '0';
    this.previewContainer.style.left = '0';
    this.previewContainer.style.width = '100vw';
    this.previewContainer.style.height = '100vh';
    this.previewContainer.style.zIndex = '9999';
    this.previewContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';

    // 更新预览内容布局
    const previewContent = this.previewContainer.querySelector('.tab-preview-content');
    if (previewContent) {
      previewContent.style.width = '90%';
      previewContent.style.height = '90%';
      previewContent.style.maxWidth = '1200px';
      previewContent.style.maxHeight = '800px';
      previewContent.style.margin = '5vh auto';
    }

    // 更新截图尺寸
    const screenshotContainer = this.previewContainer.querySelector('.preview-screenshot');
    if (screenshotContainer) {
      screenshotContainer.style.width = '50%';
      screenshotContainer.style.height = '400px';
    }

    // 添加退出提示
    this.addFullscreenExitHint();
    
    // 更新按钮状态
    this.updatePreviewModeButton(true);
    
    this.app.setStatus('已进入全屏预览模式（按 Escape 退出）');
  }

  /**
   * 退出全屏预览模式
   */
  exitFullscreenMode() {
    this.previewContainer.classList.remove('fullscreen-mode');
    this.previewContainer.style.position = 'fixed';
    this.previewContainer.style.width = '';
    this.previewContainer.style.height = '';
    this.previewContainer.style.backgroundColor = '';

    // 恢复原始位置
    if (this.currentPreview) {
      this.positionPreview(this.currentPreview.x, this.currentPreview.y);
    }

    // 恢复预览内容布局
    const previewContent = this.previewContainer.querySelector('.tab-preview-content');
    if (previewContent) {
      previewContent.style.width = '400px';
      previewContent.style.height = '';
      previewContent.style.maxWidth = '';
      previewContent.style.maxHeight = '600px';
      previewContent.style.margin = '';
    }

    // 恢复截图尺寸
    const screenshotContainer = this.previewContainer.querySelector('.preview-screenshot');
    if (screenshotContainer) {
      screenshotContainer.style.width = '200px';
      screenshotContainer.style.height = '150px';
    }

    // 移除退出提示
    this.removeFullscreenExitHint();
    
    // 更新按钮状态
    this.updatePreviewModeButton(false);
    
    this.app.setStatus('已退出全屏预览模式');
  }

  /**
   * 添加全屏退出提示
   */
  addFullscreenExitHint() {
    const existingHint = this.previewContainer.querySelector('.fullscreen-exit-hint');
    if (existingHint) return;

    const hint = document.createElement('div');
    hint.className = 'fullscreen-exit-hint';
    hint.innerHTML = `
      <div class="hint-text">
        <span>全屏预览模式</span>
        <span class="hint-shortcut">按 Escape 退出</span>
      </div>
    `;
    hint.style.cssText = `
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 16px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 10000;
    `;

    this.previewContainer.appendChild(hint);
  }

  /**
   * 移除全屏退出提示
   */
  removeFullscreenExitHint() {
    const hint = this.previewContainer.querySelector('.fullscreen-exit-hint');
    if (hint) {
      hint.remove();
    }
  }

  /**
   * 更新预览模式按钮状态
   */
  updatePreviewModeButton(isFullscreen) {
    const modeBtn = this.previewContainer.querySelector('[data-action="mode"]');
    if (modeBtn) {
      modeBtn.textContent = isFullscreen ? '🔲' : '🔳';
      modeBtn.title = isFullscreen ? '退出全屏' : '全屏预览';
    }
  }

  /**
   * 格式化时间
   */
  formatTime(milliseconds) {
    if (!milliseconds) return '0秒';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 格式化内存
   */
  formatMemory(bytes) {
    if (!bytes) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.previewCache.clear();
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.previewTimeout) {
      clearTimeout(this.previewTimeout);
    }
    
    if (this.previewContainer) {
      this.previewContainer.remove();
    }
    
    this.clearCache();
    this.currentPreview = null;
  }
}

export default TabPreviewManager;