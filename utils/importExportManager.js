/**
 * 导入导出管理器
 * 支持JSON、CSV、HTML格式的数据导入导出
 */
class ImportExportManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.supportedFormats = ['json', 'csv', 'html'];
    this.initializeImportExport();
  }

  /**
   * 初始化导入导出功能
   */
  initializeImportExport() {
    this.createImportExportUI();
    this.bindImportExportEvents();
  }

  /**
   * 创建导入导出UI
   */
  createImportExportUI() {
    // 检查是否已经存在导入导出按钮
    const existingBtn = document.querySelector('.import-export-btn');
    if (existingBtn) return;

    // 在工具栏右侧添加导入导出按钮
    const toolbarRight = document.querySelector('.toolbar-right');
    if (!toolbarRight) return;

    const importExportBtn = document.createElement('button');
    importExportBtn.className = 'btn btn-icon import-export-btn';
    importExportBtn.innerHTML = '<span class="icon">📤</span>';
    importExportBtn.title = '导入导出';
    importExportBtn.addEventListener('click', () => this.showImportExportDialog());

    toolbarRight.appendChild(importExportBtn);
  }

  /**
   * 显示导入导出对话框
   */
  showImportExportDialog() {
    const dialog = document.createElement('div');
    dialog.className = 'import-export-dialog';
    dialog.innerHTML = `
      <div class="dialog-backdrop" onclick="this.parentElement.remove()"></div>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>导入导出标签页数据</h3>
          <button class="btn btn-icon close-dialog" onclick="this.closest('.import-export-dialog').remove()">✕</button>
        </div>
        
        <div class="dialog-body">
          <div class="import-export-tabs">
            <button class="tab-btn active" data-tab="export">导出</button>
            <button class="tab-btn" data-tab="import">导入</button>
          </div>
          
          <div class="tab-content active" id="export-content">
            <div class="export-section">
              <h4>选择导出格式</h4>
              <div class="format-selection">
                <label class="format-option">
                  <input type="radio" name="exportFormat" value="json" checked>
                  <span class="format-label">
                    <strong>JSON</strong>
                    <small>完整的标签页数据和元数据</small>
                  </span>
                </label>
                <label class="format-option">
                  <input type="radio" name="exportFormat" value="csv">
                  <span class="format-label">
                    <strong>CSV</strong>
                    <small>表格格式，适合Excel处理</small>
                  </span>
                </label>
                <label class="format-option">
                  <input type="radio" name="exportFormat" value="html">
                  <span class="format-label">
                    <strong>HTML</strong>
                    <small>网页格式，包含书签链接</small>
                  </span>
                </label>
              </div>
              
              <div class="export-options">
                <h4>导出选项</h4>
                <div class="option-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="includeMetadata" checked>
                    <span>包含元数据（备注、标签、分类等）</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="includeClosedTabs">
                    <span>包含已关闭的标签页</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="includeWindowInfo" checked>
                    <span>包含窗口信息</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="includeTimestamps" checked>
                    <span>包含时间戳</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="import-content">
            <div class="import-section">
              <h4>选择导入文件</h4>
              <div class="file-upload-area">
                <input type="file" id="importFile" accept=".json,.csv,.html" style="display: none;">
                <div class="upload-zone" onclick="document.getElementById('importFile').click()">
                  <div class="upload-icon">📁</div>
                  <div class="upload-text">点击选择文件或拖拽文件到这里</div>
                  <div class="upload-hint">支持 JSON、CSV、HTML 格式</div>
                </div>
              </div>
              
              <div class="import-options" style="display: none;">
                <h4>导入选项</h4>
                <div class="option-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="mergeWithExisting" checked>
                    <span>与现有数据合并</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="openImportedTabs">
                    <span>导入后打开标签页</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="preserveMetadata" checked>
                    <span>保留元数据</span>
                  </label>
                </div>
              </div>
              
              <div class="import-preview" style="display: none;">
                <h4>导入预览</h4>
                <div class="preview-content" id="importPreview"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <button class="btn btn-secondary" onclick="this.closest('.import-export-dialog').remove()">取消</button>
          <button class="btn btn-primary" id="executeAction">导出</button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);
    this.bindDialogEvents(dialog);
  }

  /**
   * 绑定对话框事件
   */
  bindDialogEvents(dialog) {
    // 标签页切换
    const tabBtns = dialog.querySelectorAll('.tab-btn');
    const tabContents = dialog.querySelectorAll('.tab-content');
    const executeBtn = dialog.querySelector('#executeAction');

    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const tab = btn.dataset.tab;
        
        // 切换标签页状态
        tabBtns.forEach(b => b.classList.remove('active'));
        tabContents.forEach(c => c.classList.remove('active'));
        
        btn.classList.add('active');
        dialog.querySelector(`#${tab}-content`).classList.add('active');
        
        // 更新按钮文本
        executeBtn.textContent = tab === 'export' ? '导出' : '导入';
      });
    });

    // 文件选择
    const fileInput = dialog.querySelector('#importFile');
    const uploadZone = dialog.querySelector('.upload-zone');
    const importOptions = dialog.querySelector('.import-options');
    const importPreview = dialog.querySelector('.import-preview');

    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        this.handleFileSelect(file, importOptions, importPreview);
      }
    });

    // 拖拽上传
    uploadZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadZone.classList.add('drag-over');
    });

    uploadZone.addEventListener('dragleave', () => {
      uploadZone.classList.remove('drag-over');
    });

    uploadZone.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadZone.classList.remove('drag-over');
      const file = e.dataTransfer.files[0];
      if (file) {
        fileInput.files = e.dataTransfer.files;
        this.handleFileSelect(file, importOptions, importPreview);
      }
    });

    // 执行按钮
    executeBtn.addEventListener('click', () => {
      const activeTab = dialog.querySelector('.tab-btn.active').dataset.tab;
      if (activeTab === 'export') {
        this.executeExport(dialog);
      } else {
        this.executeImport(dialog);
      }
    });
  }

  /**
   * 处理文件选择
   */
  async handleFileSelect(file, importOptions, importPreview) {
    try {
      const content = await this.readFile(file);
      const data = await this.parseImportData(content, file.name);
      
      if (data) {
        importOptions.style.display = 'block';
        importPreview.style.display = 'block';
        
        // 显示预览
        const previewContent = importPreview.querySelector('#importPreview');
        previewContent.innerHTML = this.generateImportPreview(data);
      }
    } catch (error) {
      console.error('文件解析失败:', error);
      this.app.setStatus('文件解析失败，请检查文件格式');
    }
  }

  /**
   * 读取文件内容
   */
  readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  /**
   * 解析导入数据
   */
  async parseImportData(content, filename) {
    const extension = filename.split('.').pop().toLowerCase();
    
    switch (extension) {
      case 'json':
        return this.parseJsonData(content);
      case 'csv':
        return this.parseCsvData(content);
      case 'html':
        return this.parseHtmlData(content);
      default:
        throw new Error('不支持的文件格式');
    }
  }

  /**
   * 解析JSON数据
   */
  parseJsonData(content) {
    try {
      const data = JSON.parse(content);
      
      // 验证数据结构
      if (!data.tabs || !Array.isArray(data.tabs)) {
        throw new Error('无效的JSON数据结构');
      }
      
      return {
        format: 'json',
        tabs: data.tabs,
        metadata: data.metadata || {},
        timestamp: data.timestamp || Date.now()
      };
    } catch (error) {
      throw new Error('JSON解析失败: ' + error.message);
    }
  }

  /**
   * 解析CSV数据
   */
  parseCsvData(content) {
    const lines = content.split('\\n');
    const headers = lines[0].split(',').map(h => h.trim());
    const tabs = [];
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
      const tab = {};
      
      headers.forEach((header, index) => {
        tab[header] = values[index] || '';
      });
      
      tabs.push(tab);
    }
    
    return {
      format: 'csv',
      tabs: tabs,
      metadata: {},
      timestamp: Date.now()
    };
  }

  /**
   * 解析HTML数据
   */
  parseHtmlData(content) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const links = doc.querySelectorAll('a[href]');
    
    const tabs = Array.from(links).map(link => ({
      title: link.textContent.trim(),
      url: link.href,
      notes: link.title || '',
      tags: [],
      category: '',
      priority: 3,
      rating: 0,
      isBookmarked: false,
      isArchived: false
    }));
    
    return {
      format: 'html',
      tabs: tabs,
      metadata: {},
      timestamp: Date.now()
    };
  }

  /**
   * 生成导入预览
   */
  generateImportPreview(data) {
    const tabCount = data.tabs.length;
    const format = data.format.toUpperCase();
    
    return `
      <div class="preview-summary">
        <div class="preview-stat">
          <span class="stat-label">格式:</span>
          <span class="stat-value">${format}</span>
        </div>
        <div class="preview-stat">
          <span class="stat-label">标签页数量:</span>
          <span class="stat-value">${tabCount}</span>
        </div>
        <div class="preview-stat">
          <span class="stat-label">导入时间:</span>
          <span class="stat-value">${new Date(data.timestamp).toLocaleString()}</span>
        </div>
      </div>
      
      <div class="preview-tabs">
        <h5>标签页预览（前5个）：</h5>
        ${data.tabs.slice(0, 5).map(tab => `
          <div class="preview-tab-item">
            <div class="preview-tab-title">${tab.title}</div>
            <div class="preview-tab-url">${tab.url}</div>
          </div>
        `).join('')}
        ${tabCount > 5 ? `<div class="preview-more">... 还有 ${tabCount - 5} 个标签页</div>` : ''}
      </div>
    `;
  }

  /**
   * 执行导出
   */
  async executeExport(dialog) {
    try {
      const format = dialog.querySelector('input[name="exportFormat"]:checked').value;
      const includeMetadata = dialog.querySelector('#includeMetadata').checked;
      const includeClosedTabs = dialog.querySelector('#includeClosedTabs').checked;
      const includeWindowInfo = dialog.querySelector('#includeWindowInfo').checked;
      const includeTimestamps = dialog.querySelector('#includeTimestamps').checked;

      const options = {
        format,
        includeMetadata,
        includeClosedTabs,
        includeWindowInfo,
        includeTimestamps
      };

      const data = await this.generateExportData(options);
      let content, filename, mimeType;

      switch (format) {
        case 'json':
          content = JSON.stringify(data, null, 2);
          filename = `tab-manager-export-${Date.now()}.json`;
          mimeType = 'application/json';
          break;
        case 'csv':
          content = this.generateCsvContent(data);
          filename = `tab-manager-export-${Date.now()}.csv`;
          mimeType = 'text/csv';
          break;
        case 'html':
          content = this.generateHtmlContent(data);
          filename = `tab-manager-export-${Date.now()}.html`;
          mimeType = 'text/html';
          break;
      }

      this.downloadFile(content, filename, mimeType);
      this.app.setStatus(`导出成功: ${filename}`);
      dialog.remove();
      
    } catch (error) {
      console.error('导出失败:', error);
      this.app.setStatus('导出失败，请重试');
    }
  }

  /**
   * 生成导出数据
   */
  async generateExportData(options) {
    const data = {
      metadata: {
        exportTime: new Date().toISOString(),
        version: '1.0',
        format: options.format,
        options: options
      },
      tabs: [],
      windows: []
    };

    // 收集标签页数据
    for (const group of this.app.windowGroups) {
      if (options.includeWindowInfo) {
        data.windows.push({
          id: group.id,
          title: group.title,
          customName: group.customName,
          focused: group.focused,
          incognito: group.incognito,
          tabCount: group.tabs.length
        });
      }

      for (const tab of group.tabs) {
        const tabData = {
          id: tab.id,
          title: tab.title,
          url: tab.url,
          favIconUrl: tab.favIconUrl,
          active: tab.active,
          pinned: tab.pinned,
          windowId: tab.windowId,
          index: tab.index
        };

        if (options.includeMetadata) {
          const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
          tabData.metadata = {
            notes: metadata.notes,
            tags: metadata.tags,
            category: metadata.category,
            priority: metadata.priority,
            rating: metadata.rating,
            isBookmarked: metadata.isBookmarked,
            isArchived: metadata.isArchived
          };

          if (options.includeTimestamps) {
            tabData.metadata.createdTime = metadata.createdTime;
            tabData.metadata.lastModified = metadata.lastModified;
            tabData.metadata.lastVisitTime = metadata.lastVisitTime;
          }
        }

        data.tabs.push(tabData);
      }
    }

    return data;
  }

  /**
   * 生成CSV内容
   */
  generateCsvContent(data) {
    const headers = ['Title', 'URL', 'Window ID', 'Active', 'Pinned'];
    
    if (data.metadata.options.includeMetadata) {
      headers.push('Notes', 'Tags', 'Category', 'Priority', 'Rating', 'Bookmarked', 'Archived');
    }
    
    if (data.metadata.options.includeTimestamps) {
      headers.push('Created Time', 'Last Modified', 'Last Visit');
    }

    const rows = [headers.join(',')];
    
    data.tabs.forEach(tab => {
      const row = [
        `"${tab.title}"`,
        `"${tab.url}"`,
        tab.windowId,
        tab.active,
        tab.pinned
      ];
      
      if (data.metadata.options.includeMetadata && tab.metadata) {
        row.push(
          `"${tab.metadata.notes}"`,
          `"${tab.metadata.tags.join(';')}"`,
          `"${tab.metadata.category}"`,
          tab.metadata.priority,
          tab.metadata.rating,
          tab.metadata.isBookmarked,
          tab.metadata.isArchived
        );
      }
      
      if (data.metadata.options.includeTimestamps && tab.metadata) {
        row.push(
          tab.metadata.createdTime,
          tab.metadata.lastModified,
          tab.metadata.lastVisitTime
        );
      }
      
      rows.push(row.join(','));
    });

    return rows.join('\\n');
  }

  /**
   * 生成HTML内容
   */
  generateHtmlContent(data) {
    const timestamp = new Date(data.metadata.exportTime).toLocaleString();
    
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Chrome标签页管理器 - 导出书签</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
    .window-group { margin-bottom: 30px; }
    .window-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }
    .tab-list { margin-left: 20px; }
    .tab-item { margin-bottom: 8px; }
    .tab-link { color: #1a73e8; text-decoration: none; }
    .tab-link:hover { text-decoration: underline; }
    .tab-url { font-size: 12px; color: #666; margin-left: 10px; }
    .tab-notes { font-size: 11px; color: #999; font-style: italic; }
    .tab-tags { font-size: 11px; color: #0066cc; }
    .stats { background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 20px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>Chrome标签页管理器 - 导出书签</h1>
    <p>导出时间: ${timestamp}</p>
    <p>总标签页数: ${data.tabs.length}</p>
    <p>总窗口数: ${data.windows.length}</p>
  </div>

  ${data.windows.map(window => `
    <div class="window-group">
      <div class="window-title">${window.customName || window.title}</div>
      <div class="tab-list">
        ${data.tabs.filter(tab => tab.windowId === window.id).map(tab => `
          <div class="tab-item">
            <a href="${tab.url}" class="tab-link">${tab.title}</a>
            <span class="tab-url">(${tab.url})</span>
            ${tab.metadata && tab.metadata.notes ? `<div class="tab-notes">备注: ${tab.metadata.notes}</div>` : ''}
            ${tab.metadata && tab.metadata.tags.length > 0 ? `<div class="tab-tags">标签: ${tab.metadata.tags.join(', ')}</div>` : ''}
          </div>
        `).join('')}
      </div>
    </div>
  `).join('')}

  <div class="stats">
    <h3>导出统计</h3>
    <p>导出格式: HTML</p>
    <p>包含元数据: ${data.metadata.options.includeMetadata ? '是' : '否'}</p>
    <p>包含窗口信息: ${data.metadata.options.includeWindowInfo ? '是' : '否'}</p>
    <p>包含时间戳: ${data.metadata.options.includeTimestamps ? '是' : '否'}</p>
  </div>
</body>
</html>
    `;
  }

  /**
   * 下载文件
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }

  /**
   * 执行导入
   */
  async executeImport(dialog) {
    try {
      const fileInput = dialog.querySelector('#importFile');
      const file = fileInput.files[0];
      
      if (!file) {
        this.app.setStatus('请选择要导入的文件');
        return;
      }

      const mergeWithExisting = dialog.querySelector('#mergeWithExisting').checked;
      const openImportedTabs = dialog.querySelector('#openImportedTabs').checked;
      const preserveMetadata = dialog.querySelector('#preserveMetadata').checked;

      const content = await this.readFile(file);
      const data = await this.parseImportData(content, file.name);

      const options = {
        mergeWithExisting,
        openImportedTabs,
        preserveMetadata
      };

      await this.importData(data, options);
      
      this.app.setStatus(`导入成功: ${data.tabs.length} 个标签页`);
      dialog.remove();
      
      // 刷新数据显示
      await this.app.refreshData();
      
    } catch (error) {
      console.error('导入失败:', error);
      this.app.setStatus('导入失败: ' + error.message);
    }
  }

  /**
   * 导入数据
   */
  async importData(data, options) {
    let importedCount = 0;
    
    for (const tabData of data.tabs) {
      try {
        // 检查是否已存在相同URL的标签页
        if (options.mergeWithExisting) {
          const existingTab = this.findExistingTab(tabData.url);
          if (existingTab) {
            // 合并元数据
            if (options.preserveMetadata && tabData.metadata) {
              await this.mergeTabMetadata(existingTab.id, tabData.metadata);
            }
            continue;
          }
        }

        // 创建新标签页
        if (options.openImportedTabs) {
          const newTab = await this.createTab(tabData);
          if (newTab && options.preserveMetadata && tabData.metadata) {
            await this.importTabMetadata(newTab.id, tabData.metadata);
          }
        } else {
          // 仅保存元数据，不打开标签页
          if (options.preserveMetadata && tabData.metadata) {
            await this.saveTabMetadata(tabData.url, tabData.metadata);
          }
        }

        importedCount++;
      } catch (error) {
        console.error('导入标签页失败:', tabData.title, error);
      }
    }

    return importedCount;
  }

  /**
   * 查找已存在的标签页
   */
  findExistingTab(url) {
    for (const group of this.app.windowGroups) {
      const tab = group.tabs.find(t => t.url === url);
      if (tab) return tab;
    }
    return null;
  }

  /**
   * 创建标签页
   */
  async createTab(tabData) {
    try {
      const response = await this.app.sendMessage('OPEN_TAB_NEW_WINDOW', {
        url: tabData.url
      });
      
      if (response) {
        return { id: Date.now(), url: tabData.url }; // 简化的返回值
      }
    } catch (error) {
      console.error('创建标签页失败:', error);
    }
    return null;
  }

  /**
   * 导入标签页元数据
   */
  async importTabMetadata(tabId, metadata) {
    try {
      await this.app.tabNotesManager.updateTabNotes(tabId, metadata.notes || '');
      await this.app.tabNotesManager.setTabCategory(tabId, metadata.category || '');
      await this.app.tabNotesManager.setTabPriority(tabId, metadata.priority || 3);
      await this.app.tabNotesManager.setTabRating(tabId, metadata.rating || 0);
      
      if (metadata.isBookmarked) {
        await this.app.tabNotesManager.toggleTabBookmark(tabId);
      }
      
      if (metadata.tags && metadata.tags.length > 0) {
        for (const tag of metadata.tags) {
          await this.app.tabNotesManager.addTabTag(tabId, tag);
        }
      }
    } catch (error) {
      console.error('导入标签页元数据失败:', error);
    }
  }

  /**
   * 合并标签页元数据
   */
  async mergeTabMetadata(tabId, importedMetadata) {
    try {
      const existingMetadata = this.app.tabNotesManager.getTabMetadata(tabId);
      
      // 合并备注
      if (importedMetadata.notes && !existingMetadata.notes) {
        await this.app.tabNotesManager.updateTabNotes(tabId, importedMetadata.notes);
      }
      
      // 合并标签
      if (importedMetadata.tags && importedMetadata.tags.length > 0) {
        for (const tag of importedMetadata.tags) {
          if (!existingMetadata.tags.includes(tag)) {
            await this.app.tabNotesManager.addTabTag(tabId, tag);
          }
        }
      }
      
      // 合并其他元数据
      if (!existingMetadata.category && importedMetadata.category) {
        await this.app.tabNotesManager.setTabCategory(tabId, importedMetadata.category);
      }
      
      if (importedMetadata.priority > existingMetadata.priority) {
        await this.app.tabNotesManager.setTabPriority(tabId, importedMetadata.priority);
      }
      
      if (importedMetadata.rating > existingMetadata.rating) {
        await this.app.tabNotesManager.setTabRating(tabId, importedMetadata.rating);
      }
      
    } catch (error) {
      console.error('合并标签页元数据失败:', error);
    }
  }

  /**
   * 保存标签页元数据（不打开标签页）
   */
  async saveTabMetadata(url, metadata) {
    try {
      // 这里可以将元数据保存到本地存储
      // 以便将来打开相同URL的标签页时恢复
      const savedMetadata = await chrome.storage.local.get(['savedTabMetadata']);
      const metadataStore = savedMetadata.savedTabMetadata || {};
      
      metadataStore[url] = {
        ...metadata,
        savedTime: Date.now()
      };
      
      await chrome.storage.local.set({ savedTabMetadata: metadataStore });
    } catch (error) {
      console.error('保存标签页元数据失败:', error);
    }
  }

  /**
   * 绑定导入导出事件
   */
  bindImportExportEvents() {
    // 监听键盘快捷键
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'E') {
        e.preventDefault();
        this.showImportExportDialog();
      }
    });
  }

  /**
   * 获取导入导出统计
   */
  getImportExportStats() {
    const totalTabs = this.app.windowGroups.reduce((sum, group) => sum + group.tabs.length, 0);
    const totalWindows = this.app.windowGroups.length;
    
    return {
      totalTabs,
      totalWindows,
      supportedFormats: this.supportedFormats,
      lastExportTime: localStorage.getItem('lastExportTime'),
      lastImportTime: localStorage.getItem('lastImportTime')
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    const importExportBtn = document.querySelector('.import-export-btn');
    if (importExportBtn) {
      importExportBtn.remove();
    }
    
    const dialog = document.querySelector('.import-export-dialog');
    if (dialog) {
      dialog.remove();
    }
  }
}

export default ImportExportManager;