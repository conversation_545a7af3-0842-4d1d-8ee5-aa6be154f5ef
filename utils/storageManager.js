/**
 * 数据存储管理类
 * 负责处理扩展数据的本地存储和会话管理
 */
class StorageManager {
  constructor() {
    this.STORAGE_KEYS = {
      WINDOW_GROUPS: 'windowGroups',
      USER_SETTINGS: 'userSettings',
      COLLAPSED_GROUPS: 'collapsedGroups',
      CUSTOM_NAMES: 'customNames',
      SEARCH_HISTORY: 'searchHistory'
    };
  }

  /**
   * 保存窗口组数据
   */
  async saveWindowGroups(windowGroups) {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.WINDOW_GROUPS]: windowGroups
      });
      return true;
    } catch (error) {
      console.error('保存窗口组失败:', error);
      return false;
    }
  }

  /**
   * 加载窗口组数据
   */
  async loadWindowGroups() {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEYS.WINDOW_GROUPS);
      return result[this.STORAGE_KEYS.WINDOW_GROUPS] || [];
    } catch (error) {
      console.error('加载窗口组失败:', error);
      return [];
    }
  }

  /**
   * 保存用户设置
   */
  async saveUserSettings(settings) {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.USER_SETTINGS]: settings
      });
      return true;
    } catch (error) {
      console.error('保存用户设置失败:', error);
      return false;
    }
  }

  /**
   * 加载用户设置
   */
  async loadUserSettings() {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEYS.USER_SETTINGS);
      return result[this.STORAGE_KEYS.USER_SETTINGS] || this.getDefaultSettings();
    } catch (error) {
      console.error('加载用户设置失败:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * 获取默认设置
   */
  getDefaultSettings() {
    return {
      autoCollapse: false,
      showFavicons: true,
      showTabCount: true,
      sortOrder: 'recent',
      theme: 'light',
      compactMode: false
    };
  }

  /**
   * 保存折叠状态
   */
  async saveCollapsedGroups(collapsedGroups) {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.COLLAPSED_GROUPS]: collapsedGroups
      });
    } catch (error) {
      console.error('保存折叠状态失败:', error);
    }
  }

  /**
   * 加载折叠状态
   */
  async loadCollapsedGroups() {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEYS.COLLAPSED_GROUPS);
      return result[this.STORAGE_KEYS.COLLAPSED_GROUPS] || {};
    } catch (error) {
      console.error('加载折叠状态失败:', error);
      return {};
    }
  }

  /**
   * 保存自定义窗口名称
   */
  async saveCustomNames(customNames) {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.CUSTOM_NAMES]: customNames
      });
    } catch (error) {
      console.error('保存自定义名称失败:', error);
    }
  }

  /**
   * 加载自定义窗口名称
   */
  async loadCustomNames() {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEYS.CUSTOM_NAMES);
      return result[this.STORAGE_KEYS.CUSTOM_NAMES] || {};
    } catch (error) {
      console.error('加载自定义名称失败:', error);
      return {};
    }
  }

  /**
   * 清理存储数据
   */
  async clearAllData() {
    try {
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('清理数据失败:', error);
      return false;
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      return {
        used: usage,
        quota: chrome.storage.local.QUOTA_BYTES,
        percentage: (usage / chrome.storage.local.QUOTA_BYTES) * 100
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return { used: 0, quota: 0, percentage: 0 };
    }
  }
}

// 导出存储管理器实例
const storageManager = new StorageManager();

// 在service worker中全局可用
if (typeof globalThis !== 'undefined') {
  globalThis.storageManager = storageManager;
}

// 为Chrome扩展环境提供全局访问
if (typeof window !== 'undefined') {
  window.StorageManager = StorageManager;
  window.storageManager = storageManager;
}

// export default StorageManager; // 注释掉ES6模块导出，改用全局变量