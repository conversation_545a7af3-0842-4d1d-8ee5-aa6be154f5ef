/**
 * 拖拽排序管理器
 * 支持标签页和窗口组的拖拽排序
 */
class DragDropManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.draggedElement = null;
    this.draggedData = null;
    this.dropIndicator = null;
    this.isDragging = false;
    this.isEnabled = true;
    
    this.initializeDragDrop();
  }

  /**
   * 初始化拖拽功能
   */
  initializeDragDrop() {
    // 监听拖拽事件
    document.addEventListener('dragstart', this.handleDragStart.bind(this));
    document.addEventListener('dragover', this.handleDragOver.bind(this));
    document.addEventListener('drop', this.handleDrop.bind(this));
    document.addEventListener('dragend', this.handleDragEnd.bind(this));
    document.addEventListener('dragenter', this.handleDragEnter.bind(this));
    document.addEventListener('dragleave', this.handleDragLeave.bind(this));
  }

  /**
   * 启用拖拽功能
   */
  enable() {
    this.isEnabled = true;
    this.updateDragableElements();
  }

  /**
   * 禁用拖拽功能
   */
  disable() {
    this.isEnabled = false;
    this.updateDragableElements();
  }

  /**
   * 更新可拖拽元素
   */
  updateDragableElements() {
    // 更新标签页元素
    document.querySelectorAll('.tab-item').forEach(item => {
      item.draggable = this.isEnabled;
    });

    // 更新窗口组头部元素
    document.querySelectorAll('.window-group-header').forEach(header => {
      header.draggable = this.isEnabled;
    });
  }

  /**
   * 开始拖拽
   */
  handleDragStart(e) {
    if (!this.isEnabled) return;
    
    const tabItem = e.target.closest('.tab-item');
    const windowHeader = e.target.closest('.window-group-header');
    
    if (tabItem) {
      this.startTabDrag(e, tabItem);
    } else if (windowHeader) {
      this.startWindowGroupDrag(e, windowHeader);
    } else {
      e.preventDefault();
    }
  }

  /**
   * 开始标签页拖拽
   */
  startTabDrag(e, tabItem) {
    this.isDragging = true;
    this.draggedElement = tabItem;
    this.draggedData = {
      type: 'tab',
      tabId: parseInt(tabItem.dataset.tabId),
      windowId: parseInt(tabItem.dataset.windowId),
      element: tabItem
    };
    
    tabItem.classList.add('dragging');
    
    // 创建拖拽图像
    this.createDragImage(e, tabItem);
    
    e.dataTransfer.setData('text/plain', JSON.stringify(this.draggedData));
    e.dataTransfer.effectAllowed = 'move';
    
    console.log('Started tab drag:', this.draggedData);
  }

  /**
   * 开始窗口组拖拽
   */
  startWindowGroupDrag(e, windowHeader) {
    this.isDragging = true;
    this.draggedElement = windowHeader.closest('.window-group');
    this.draggedData = {
      type: 'window-group',
      windowId: parseInt(windowHeader.dataset.windowId),
      element: this.draggedElement
    };
    
    this.draggedElement.classList.add('dragging');
    
    // 创建拖拽图像
    this.createDragImage(e, windowHeader);
    
    e.dataTransfer.setData('text/plain', JSON.stringify(this.draggedData));
    e.dataTransfer.effectAllowed = 'move';
    
    console.log('Started window group drag:', this.draggedData);
  }

  /**
   * 创建拖拽图像
   */
  createDragImage(e, element) {
    const dragImage = element.cloneNode(true);
    dragImage.style.position = 'absolute';
    dragImage.style.top = '-1000px';
    dragImage.style.left = '-1000px';
    dragImage.style.opacity = '0.8';
    dragImage.style.transform = 'rotate(2deg)';
    dragImage.style.pointerEvents = 'none';
    
    document.body.appendChild(dragImage);
    
    e.dataTransfer.setDragImage(dragImage, 50, 20);
    
    // 清理临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  }

  /**
   * 拖拽进入处理
   */
  handleDragEnter(e) {
    if (!this.isDragging) return;
    
    const target = this.findDropTarget(e.target);
    if (target && target !== this.draggedElement) {
      target.classList.add('drag-hover');
    }
  }

  /**
   * 拖拽离开处理
   */
  handleDragLeave(e) {
    if (!this.isDragging) return;
    
    const target = this.findDropTarget(e.target);
    if (target) {
      target.classList.remove('drag-hover');
    }
  }

  /**
   * 拖拽悬停处理
   */
  handleDragOver(e) {
    if (!this.isDragging) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    // 查找可放置的目标
    const dropTarget = this.findDropTarget(e.target);
    if (dropTarget && dropTarget !== this.draggedElement) {
      this.showDropIndicator(dropTarget, e);
    }
  }

  /**
   * 查找放置目标
   */
  findDropTarget(element) {
    // 向上查找直到找到可放置的目标
    let current = element;
    while (current && current !== document.body) {
      if (current.classList.contains('tab-item') || 
          current.classList.contains('window-group') ||
          current.classList.contains('tab-list')) {
        return current;
      }
      current = current.parentElement;
    }
    return null;
  }

  /**
   * 显示放置指示器
   */
  showDropIndicator(target, e) {
    this.hideDropIndicator();
    
    if (!this.canDropOn(target)) {
      return;
    }
    
    this.dropIndicator = document.createElement('div');
    this.dropIndicator.className = 'drop-indicator';
    
    const rect = target.getBoundingClientRect();
    const isAbove = e.clientY < rect.top + rect.height / 2;
    
    this.dropIndicator.style.position = 'absolute';
    this.dropIndicator.style.left = `${rect.left}px`;
    this.dropIndicator.style.right = `${window.innerWidth - rect.right}px`;
    this.dropIndicator.style.top = `${isAbove ? rect.top - 1 : rect.bottom - 1}px`;
    this.dropIndicator.style.height = '2px';
    this.dropIndicator.style.zIndex = '1000';
    
    document.body.appendChild(this.dropIndicator);
  }

  /**
   * 检查是否可以放置在目标上
   */
  canDropOn(target) {
    if (!this.draggedData) return false;
    
    if (this.draggedData.type === 'tab') {
      // 标签页可以放置在其他标签页上或窗口组上
      return target.classList.contains('tab-item') || 
             target.classList.contains('window-group') ||
             target.classList.contains('tab-list');
    } else if (this.draggedData.type === 'window-group') {
      // 窗口组只能放置在其他窗口组上
      return target.classList.contains('window-group');
    }
    
    return false;
  }

  /**
   * 隐藏放置指示器
   */
  hideDropIndicator() {
    if (this.dropIndicator) {
      this.dropIndicator.remove();
      this.dropIndicator = null;
    }
  }

  /**
   * 处理放置
   */
  async handleDrop(e) {
    if (!this.isDragging) return;
    
    e.preventDefault();
    this.hideDropIndicator();
    
    // 清除悬停效果
    document.querySelectorAll('.drag-hover').forEach(el => {
      el.classList.remove('drag-hover');
    });
    
    try {
      const dropTarget = this.findDropTarget(e.target);
      if (!dropTarget || dropTarget === this.draggedElement) {
        return;
      }
      
      if (this.draggedData.type === 'tab') {
        await this.handleTabDrop(dropTarget, e);
      } else if (this.draggedData.type === 'window-group') {
        await this.handleWindowGroupDrop(dropTarget, e);
      }
    } catch (error) {
      console.error('拖拽放置失败:', error);
      this.app.setStatus('拖拽操作失败');
    }
  }

  /**
   * 处理标签页放置
   */
  async handleTabDrop(dropTarget, e) {
    if (dropTarget.classList.contains('tab-item')) {
      // 标签页间重新排序
      await this.reorderTabs(dropTarget, e);
    } else if (dropTarget.classList.contains('window-group') || 
               dropTarget.classList.contains('tab-list')) {
      // 移动到不同窗口组
      await this.moveTabToWindowGroup(dropTarget);
    }
  }

  /**
   * 标签页重新排序
   */
  async reorderTabs(dropTarget, e) {
    const targetTabId = parseInt(dropTarget.dataset.tabId);
    const targetWindowId = parseInt(dropTarget.dataset.windowId);
    
    if (this.draggedData.windowId === targetWindowId) {
      // 在同一窗口内重新排序
      const success = await this.app.sendMessage('REORDER_TABS', {
        tabId: this.draggedData.tabId,
        targetTabId: targetTabId,
        windowId: targetWindowId
      });
      
      if (success) {
        this.app.setStatus('标签页已重新排序');
        await this.app.refreshData();
      } else {
        this.app.setStatus('重新排序失败');
      }
    } else {
      // 移动到不同窗口
      await this.moveTabToWindow(targetWindowId, targetTabId);
    }
  }

  /**
   * 移动标签页到窗口组
   */
  async moveTabToWindowGroup(windowGroup) {
    const targetWindowId = parseInt(windowGroup.dataset.windowId);
    if (targetWindowId && targetWindowId !== this.draggedData.windowId) {
      await this.moveTabToWindow(targetWindowId);
    }
  }

  /**
   * 移动标签页到窗口
   */
  async moveTabToWindow(targetWindowId, insertBeforeTabId = null) {
    const success = await this.app.sendMessage('MOVE_TAB_TO_WINDOW', {
      tabId: this.draggedData.tabId,
      targetWindowId: targetWindowId,
      insertBeforeTabId: insertBeforeTabId
    });
    
    if (success) {
      this.app.setStatus('标签页已移动');
      await this.app.refreshData();
    } else {
      this.app.setStatus('移动失败');
    }
  }

  /**
   * 处理窗口组放置
   */
  async handleWindowGroupDrop(dropTarget, e) {
    if (dropTarget.classList.contains('window-group')) {
      // 窗口组重新排序
      await this.reorderWindowGroups(dropTarget, e);
    }
  }

  /**
   * 窗口组重新排序
   */
  async reorderWindowGroups(dropTarget, e) {
    const targetWindowId = parseInt(dropTarget.dataset.windowId);
    
    if (targetWindowId && targetWindowId !== this.draggedData.windowId) {
      const success = await this.app.sendMessage('REORDER_WINDOW_GROUPS', {
        sourceWindowId: this.draggedData.windowId,
        targetWindowId: targetWindowId
      });
      
      if (success) {
        this.app.setStatus('窗口组已重新排序');
        await this.app.refreshData();
      } else {
        this.app.setStatus('重新排序失败');
      }
    }
  }

  /**
   * 拖拽结束处理
   */
  handleDragEnd(e) {
    this.isDragging = false;
    this.hideDropIndicator();
    
    if (this.draggedElement) {
      this.draggedElement.classList.remove('dragging');
    }
    
    // 清除拖拽悬停效果
    document.querySelectorAll('.drag-hover').forEach(el => {
      el.classList.remove('drag-hover');
    });
    
    this.draggedElement = null;
    this.draggedData = null;
  }

  /**
   * 启用元素的拖拽功能
   */
  enableDragForElement(element, type) {
    if (!this.isEnabled) return;
    
    element.draggable = true;
    element.addEventListener('dragstart', (e) => {
      if (type === 'tab') {
        this.startTabDrag(e, element);
      } else if (type === 'window-group') {
        this.startWindowGroupDrag(e, element);
      }
    });
  }

  /**
   * 禁用元素的拖拽功能
   */
  disableDragForElement(element) {
    element.draggable = false;
    element.removeEventListener('dragstart', this.handleDragStart);
  }

  /**
   * 清理资源
   */
  cleanup() {
    document.removeEventListener('dragstart', this.handleDragStart);
    document.removeEventListener('dragover', this.handleDragOver);
    document.removeEventListener('drop', this.handleDrop);
    document.removeEventListener('dragend', this.handleDragEnd);
    document.removeEventListener('dragenter', this.handleDragEnter);
    document.removeEventListener('dragleave', this.handleDragLeave);
    
    this.hideDropIndicator();
    this.draggedElement = null;
    this.draggedData = null;
  }
}

export default DragDropManager;