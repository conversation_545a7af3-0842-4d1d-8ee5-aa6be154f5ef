/**
 * 操作撤销管理器
 * 支持撤销最近的操作，提升用户体验
 */
class UndoManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.undoStack = [];
    this.maxUndoSize = 10;
    this.currentNotification = null;
    this.notificationTimeout = null;
  }

  /**
   * 记录操作到撤销栈
   */
  recordOperation(operation) {
    // 添加时间戳
    operation.timestamp = Date.now();
    operation.id = this.generateOperationId();
    
    this.undoStack.push(operation);
    
    // 保持撤销栈大小
    if (this.undoStack.length > this.maxUndoSize) {
      this.undoStack.shift();
    }
    
    console.log('记录操作:', operation.type, operation.id);
  }

  /**
   * 生成操作ID
   */
  generateOperationId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 记录标签页关闭操作
   */
  recordTabClose(tabIds, tabsData) {
    const operation = {
      type: 'CLOSE_TABS',
      tabIds: Array.isArray(tabIds) ? tabIds : [tabIds],
      tabsData: Array.isArray(tabsData) ? tabsData : [tabsData],
      description: `关闭了 ${Array.isArray(tabIds) ? tabIds.length : 1} 个标签页`
    };
    
    this.recordOperation(operation);
    this.showUndoNotification(operation);
  }

  /**
   * 记录窗口关闭操作
   */
  recordWindowClose(windowId, windowData) {
    const operation = {
      type: 'CLOSE_WINDOW',
      windowId: windowId,
      windowData: windowData,
      description: `关闭了窗口 "${windowData.title || windowId}"`
    };
    
    this.recordOperation(operation);
    this.showUndoNotification(operation);
  }

  /**
   * 记录标签页移动操作
   */
  recordTabMove(tabId, fromWindowId, toWindowId, fromIndex, toIndex) {
    const operation = {
      type: 'MOVE_TAB',
      tabId: tabId,
      fromWindowId: fromWindowId,
      toWindowId: toWindowId,
      fromIndex: fromIndex,
      toIndex: toIndex,
      description: `移动了标签页`
    };
    
    this.recordOperation(operation);
    this.showUndoNotification(operation);
  }

  /**
   * 记录批量操作
   */
  recordBatchOperation(operationType, items, description) {
    const operation = {
      type: 'BATCH_OPERATION',
      operationType: operationType,
      items: items,
      description: description
    };
    
    this.recordOperation(operation);
    this.showUndoNotification(operation);
  }

  /**
   * 显示撤销通知
   */
  showUndoNotification(operation) {
    // 隐藏之前的通知
    this.hideUndoNotification();
    
    // 创建新通知
    this.currentNotification = document.createElement('div');
    this.currentNotification.className = 'undo-notification';
    this.currentNotification.innerHTML = `
      <span class="undo-message">${operation.description}</span>
      <button class="undo-btn" onclick="undoManager.undoOperation('${operation.id}')">撤销</button>
      <button class="undo-close" onclick="undoManager.hideUndoNotification()">✕</button>
    `;
    
    document.body.appendChild(this.currentNotification);
    
    // 自动隐藏通知
    this.notificationTimeout = setTimeout(() => {
      this.hideUndoNotification();
    }, 5000);
  }

  /**
   * 隐藏撤销通知
   */
  hideUndoNotification() {
    if (this.currentNotification) {
      this.currentNotification.remove();
      this.currentNotification = null;
    }
    
    if (this.notificationTimeout) {
      clearTimeout(this.notificationTimeout);
      this.notificationTimeout = null;
    }
  }

  /**
   * 执行撤销操作
   */
  async undoOperation(operationId) {
    const operationIndex = this.undoStack.findIndex(op => op.id === operationId);
    if (operationIndex === -1) {
      console.warn('未找到操作:', operationId);
      return false;
    }
    
    const operation = this.undoStack[operationIndex];
    
    try {
      let success = false;
      
      switch (operation.type) {
        case 'CLOSE_TABS':
          success = await this.undoTabClose(operation);
          break;
        case 'CLOSE_WINDOW':
          success = await this.undoWindowClose(operation);
          break;
        case 'MOVE_TAB':
          success = await this.undoTabMove(operation);
          break;
        case 'BATCH_OPERATION':
          success = await this.undoBatchOperation(operation);
          break;
        default:
          console.warn('未知操作类型:', operation.type);
      }
      
      if (success) {
        // 从撤销栈中移除已撤销的操作
        this.undoStack.splice(operationIndex, 1);
        this.hideUndoNotification();
        this.app.setStatus('操作已撤销');
        await this.app.refreshData();
      } else {
        this.app.setStatus('撤销失败');
      }
      
      return success;
    } catch (error) {
      console.error('撤销操作失败:', error);
      this.app.setStatus('撤销失败');
      return false;
    }
  }

  /**
   * 撤销标签页关闭
   */
  async undoTabClose(operation) {
    const results = [];
    
    for (const tabData of operation.tabsData) {
      try {
        // 重新打开标签页
        const success = await this.app.sendMessage('RESTORE_TAB', {
          url: tabData.url,
          windowId: tabData.windowId,
          index: tabData.index,
          active: tabData.active,
          pinned: tabData.pinned
        });
        
        results.push(success);
      } catch (error) {
        console.error('恢复标签页失败:', error);
        results.push(false);
      }
    }
    
    return results.some(result => result);
  }

  /**
   * 撤销窗口关闭
   */
  async undoWindowClose(operation) {
    try {
      // 重新创建窗口并恢复所有标签页
      const windowData = operation.windowData;
      const success = await this.app.sendMessage('RESTORE_WINDOW', {
        tabs: windowData.tabs,
        focused: windowData.focused,
        incognito: windowData.incognito
      });
      
      return success;
    } catch (error) {
      console.error('恢复窗口失败:', error);
      return false;
    }
  }

  /**
   * 撤销标签页移动
   */
  async undoTabMove(operation) {
    try {
      // 将标签页移回原位置
      const success = await this.app.sendMessage('MOVE_TAB_TO_WINDOW', {
        tabId: operation.tabId,
        targetWindowId: operation.fromWindowId,
        index: operation.fromIndex
      });
      
      return success;
    } catch (error) {
      console.error('撤销标签页移动失败:', error);
      return false;
    }
  }

  /**
   * 撤销批量操作
   */
  async undoBatchOperation(operation) {
    try {
      switch (operation.operationType) {
        case 'CLOSE_TABS':
          return await this.undoTabClose(operation);
        case 'MOVE_TABS':
          // 实现批量移动的撤销
          return await this.undoBatchTabMove(operation);
        default:
          console.warn('未知批量操作类型:', operation.operationType);
          return false;
      }
    } catch (error) {
      console.error('撤销批量操作失败:', error);
      return false;
    }
  }

  /**
   * 撤销批量标签页移动
   */
  async undoBatchTabMove(operation) {
    const results = [];
    
    for (const item of operation.items) {
      try {
        const success = await this.app.sendMessage('MOVE_TAB_TO_WINDOW', {
          tabId: item.tabId,
          targetWindowId: item.fromWindowId,
          index: item.fromIndex
        });
        
        results.push(success);
      } catch (error) {
        console.error('撤销标签页移动失败:', error);
        results.push(false);
      }
    }
    
    return results.some(result => result);
  }

  /**
   * 获取最近的操作
   */
  getRecentOperations(limit = 5) {
    return this.undoStack
      .slice(-limit)
      .reverse()
      .map(op => ({
        id: op.id,
        type: op.type,
        description: op.description,
        timestamp: op.timestamp,
        timeAgo: this.formatTimeAgo(op.timestamp)
      }));
  }

  /**
   * 格式化时间差
   */
  formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) { // 小于1分钟
      return '刚刚';
    } else if (diff < 3600000) { // 小于1小时
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    } else if (diff < 86400000) { // 小于1天
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    } else {
      const days = Math.floor(diff / 86400000);
      return `${days}天前`;
    }
  }

  /**
   * 清除撤销历史
   */
  clearUndoHistory() {
    this.undoStack = [];
    this.hideUndoNotification();
  }

  /**
   * 检查是否可以撤销
   */
  canUndo() {
    return this.undoStack.length > 0;
  }

  /**
   * 获取撤销栈大小
   */
  getUndoStackSize() {
    return this.undoStack.length;
  }

  /**
   * 创建撤销历史界面
   */
  createUndoHistoryPanel() {
    const panel = document.createElement('div');
    panel.className = 'undo-history-panel';
    panel.innerHTML = `
      <div class="undo-history-header">
        <h3>操作历史</h3>
        <button class="btn btn-small btn-secondary" onclick="undoManager.clearUndoHistory()">清除历史</button>
      </div>
      <div class="undo-history-list">
        ${this.getRecentOperations().map(op => `
          <div class="undo-history-item">
            <div class="undo-history-info">
              <div class="undo-history-description">${op.description}</div>
              <div class="undo-history-time">${op.timeAgo}</div>
            </div>
            <button class="btn btn-small btn-primary" onclick="undoManager.undoOperation('${op.id}')">撤销</button>
          </div>
        `).join('')}
      </div>
    `;
    
    return panel;
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.clearUndoHistory();
    this.hideUndoNotification();
  }
}

export default UndoManager;