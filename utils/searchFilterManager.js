/**
 * 高级搜索和筛选管理器
 * 提供多条件搜索、筛选、历史记录等功能
 */
class SearchFilterManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.currentFilters = {
      query: '',
      category: '',
      tags: [],
      priority: null,
      rating: null,
      bookmarked: null,
      archived: null,
      dateRange: null,
      domain: ''
    };
    this.searchHistory = [];
    this.maxHistorySize = 20;
    this.isAdvancedMode = false;
    
    this.initializeSearchFilters();
  }

  /**
   * 初始化搜索筛选功能
   */
  initializeSearchFilters() {
    this.loadSearchHistory();
    this.setupSearchUI();
    this.bindSearchEvents();
  }

  /**
   * 设置搜索界面
   */
  setupSearchUI() {
    const searchContainer = document.querySelector('.search-container');
    if (!searchContainer) return;

    // 创建高级搜索按钮
    const advancedBtn = document.createElement('button');
    advancedBtn.className = 'btn btn-icon advanced-search-btn';
    advancedBtn.innerHTML = '<span class="icon">🔍</span>';
    advancedBtn.title = '高级搜索';
    advancedBtn.addEventListener('click', () => this.toggleAdvancedSearch());
    
    searchContainer.appendChild(advancedBtn);

    // 创建高级搜索面板
    this.createAdvancedSearchPanel();
  }

  /**
   * 创建高级搜索面板
   */
  createAdvancedSearchPanel() {
    const panel = document.createElement('div');
    panel.className = 'advanced-search-panel';
    panel.style.display = 'none';
    panel.innerHTML = `
      <div class="panel-content">
        <div class="advanced-search-header">
          <h3>高级搜索</h3>
          <button class="btn btn-icon close-advanced-search">✕</button>
        </div>
        
        <div class="advanced-search-body">
          <div class="search-section">
            <label>搜索内容</label>
            <input type="text" id="advancedSearchQuery" class="form-control" placeholder="搜索标题、URL、备注...">
          </div>
          
          <div class="search-section">
            <label>域名筛选</label>
            <input type="text" id="domainFilter" class="form-control" placeholder="例如: github.com">
          </div>
          
          <div class="search-section">
            <label>分类筛选</label>
            <select id="categoryFilter" class="form-control">
              <option value="">全部分类</option>
              ${this.getCategoryOptions()}
            </select>
          </div>
          
          <div class="search-section">
            <label>标签筛选</label>
            <div class="tag-filter-container">
              <input type="text" id="tagFilter" class="form-control" placeholder="输入标签名称...">
              <div class="selected-tags" id="selectedTags"></div>
            </div>
          </div>
          
          <div class="search-row">
            <div class="search-section">
              <label>优先级</label>
              <select id="priorityFilter" class="form-control">
                <option value="">全部优先级</option>
                <option value="1">⭐ 最低</option>
                <option value="2">⭐⭐ 低</option>
                <option value="3">⭐⭐⭐ 普通</option>
                <option value="4">⭐⭐⭐⭐ 高</option>
                <option value="5">⭐⭐⭐⭐⭐ 最高</option>
              </select>
            </div>
            
            <div class="search-section">
              <label>评分</label>
              <select id="ratingFilter" class="form-control">
                <option value="">全部评分</option>
                <option value="1">⭐ 1星</option>
                <option value="2">⭐⭐ 2星</option>
                <option value="3">⭐⭐⭐ 3星</option>
                <option value="4">⭐⭐⭐⭐ 4星</option>
                <option value="5">⭐⭐⭐⭐⭐ 5星</option>
              </select>
            </div>
          </div>
          
          <div class="search-section">
            <label>状态筛选</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" id="bookmarkedFilter">
                <span>仅显示收藏的</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="archivedFilter">
                <span>仅显示归档的</span>
              </label>
            </div>
          </div>
          
          <div class="search-section">
            <label>创建时间</label>
            <div class="date-range-container">
              <input type="date" id="dateFrom" class="form-control">
              <span>到</span>
              <input type="date" id="dateTo" class="form-control">
            </div>
          </div>
          
          <div class="search-history-section">
            <label>搜索历史</label>
            <div class="search-history-list" id="searchHistoryList"></div>
          </div>
        </div>
        
        <div class="advanced-search-footer">
          <button class="btn btn-secondary" id="clearFilters">清除筛选</button>
          <button class="btn btn-primary" id="applyFilters">应用筛选</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
    this.bindAdvancedSearchEvents(panel);
  }

  /**
   * 获取分类选项
   */
  getCategoryOptions() {
    try {
      const categories = this.app.tabNotesManager.getCategories();
      return categories.map(cat => 
        `<option value="${cat.id}">${cat.icon} ${cat.name}</option>`
      ).join('');
    } catch (error) {
      console.error('获取分类选项失败:', error);
      return '';
    }
  }

  /**
   * 绑定搜索事件
   */
  bindSearchEvents() {
    // 基础搜索框事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      // 实时搜索
      searchInput.addEventListener('input', (e) => {
        this.handleBasicSearch(e.target.value);
      });
      
      // 键盘事件
      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          this.handleBasicSearch(e.target.value);
          this.addToSearchHistory(e.target.value);
        }
      });
    }
  }

  /**
   * 绑定高级搜索事件
   */
  bindAdvancedSearchEvents(panel) {
    // 关闭按钮
    panel.querySelector('.close-advanced-search').addEventListener('click', () => {
      this.toggleAdvancedSearch();
    });

    // 应用筛选
    panel.querySelector('#applyFilters').addEventListener('click', () => {
      this.applyAdvancedFilters();
    });

    // 清除筛选
    panel.querySelector('#clearFilters').addEventListener('click', () => {
      this.clearAllFilters();
    });

    // 标签输入
    const tagInput = panel.querySelector('#tagFilter');
    tagInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.addTagFilter(tagInput.value);
        tagInput.value = '';
      }
    });

    // 实时筛选
    const inputs = panel.querySelectorAll('input, select');
    inputs.forEach(input => {
      input.addEventListener('change', () => {
        if (this.isAdvancedMode) {
          this.updateFiltersFromUI();
        }
      });
    });
  }

  /**
   * 切换高级搜索面板
   */
  toggleAdvancedSearch() {
    this.isAdvancedMode = !this.isAdvancedMode;
    const panel = document.querySelector('.advanced-search-panel');
    
    if (this.isAdvancedMode) {
      panel.style.display = 'block';
      this.updateSearchHistory();
    } else {
      panel.style.display = 'none';
    }
    
    // 更新按钮状态
    const advancedBtn = document.querySelector('.advanced-search-btn');
    if (advancedBtn) {
      advancedBtn.classList.toggle('active', this.isAdvancedMode);
    }
  }

  /**
   * 处理基础搜索
   */
  handleBasicSearch(query) {
    this.currentFilters.query = query;
    this.executeSearch();
  }

  /**
   * 应用高级筛选
   */
  applyAdvancedFilters() {
    this.updateFiltersFromUI();
    this.executeSearch();
    this.addToSearchHistory(this.buildSearchDescription());
  }

  /**
   * 从UI更新筛选器
   */
  updateFiltersFromUI() {
    const panel = document.querySelector('.advanced-search-panel');
    if (!panel) return;

    this.currentFilters.query = panel.querySelector('#advancedSearchQuery').value;
    this.currentFilters.domain = panel.querySelector('#domainFilter').value;
    this.currentFilters.category = panel.querySelector('#categoryFilter').value;
    this.currentFilters.priority = panel.querySelector('#priorityFilter').value || null;
    this.currentFilters.rating = panel.querySelector('#ratingFilter').value || null;
    this.currentFilters.bookmarked = panel.querySelector('#bookmarkedFilter').checked || null;
    this.currentFilters.archived = panel.querySelector('#archivedFilter').checked || null;
    
    // 日期范围
    const dateFrom = panel.querySelector('#dateFrom').value;
    const dateTo = panel.querySelector('#dateTo').value;
    if (dateFrom || dateTo) {
      this.currentFilters.dateRange = { from: dateFrom, to: dateTo };
    } else {
      this.currentFilters.dateRange = null;
    }
  }

  /**
   * 添加标签筛选
   */
  addTagFilter(tagName) {
    if (!tagName.trim()) return;
    
    if (!this.currentFilters.tags.includes(tagName)) {
      this.currentFilters.tags.push(tagName);
      this.updateTagFilterUI();
    }
  }

  /**
   * 移除标签筛选
   */
  removeTagFilter(tagName) {
    const index = this.currentFilters.tags.indexOf(tagName);
    if (index > -1) {
      this.currentFilters.tags.splice(index, 1);
      this.updateTagFilterUI();
    }
  }

  /**
   * 更新标签筛选UI
   */
  updateTagFilterUI() {
    const container = document.querySelector('#selectedTags');
    if (!container) return;

    container.innerHTML = this.currentFilters.tags.map(tag => `
      <span class="tag-filter-item">
        ${tag}
        <button class="tag-remove" data-tag="${tag}">×</button>
      </span>
    `).join('');
    
    // 绑定删除事件
    container.querySelectorAll('.tag-remove').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const tag = e.target.dataset.tag;
        this.removeTagFilter(tag);
      });
    });
  }

  /**
   * 执行搜索
   */
  executeSearch() {
    const results = this.searchTabs(this.currentFilters);
    this.app.displaySearchResults(results);
  }

  /**
   * 搜索标签页
   */
  searchTabs(filters) {
    const allTabs = this.getAllTabs();
    
    return allTabs.filter(tabInfo => {
      const tab = tabInfo.tab;
      const metadata = tabInfo.metadata;
      
      // 基础查询匹配
      if (filters.query) {
        const searchText = `${tab.title} ${tab.url} ${metadata.notes}`.toLowerCase();
        if (!searchText.includes(filters.query.toLowerCase())) {
          return false;
        }
      }
      
      // 域名筛选
      if (filters.domain) {
        try {
          const tabDomain = new URL(tab.url).hostname;
          if (!tabDomain.includes(filters.domain.toLowerCase())) {
            return false;
          }
        } catch (e) {
          return false;
        }
      }
      
      // 分类筛选
      if (filters.category && metadata.category !== filters.category) {
        return false;
      }
      
      // 标签筛选
      if (filters.tags && filters.tags.length > 0) {
        const hasAllTags = filters.tags.every(tag => 
          metadata.tags.includes(tag)
        );
        if (!hasAllTags) {
          return false;
        }
      }
      
      // 优先级筛选
      if (filters.priority && metadata.priority !== parseInt(filters.priority)) {
        return false;
      }
      
      // 评分筛选
      if (filters.rating && metadata.rating !== parseInt(filters.rating)) {
        return false;
      }
      
      // 收藏状态筛选
      if (filters.bookmarked !== null && metadata.isBookmarked !== filters.bookmarked) {
        return false;
      }
      
      // 归档状态筛选
      if (filters.archived !== null && metadata.isArchived !== filters.archived) {
        return false;
      }
      
      // 日期范围筛选
      if (filters.dateRange) {
        const createdDate = new Date(metadata.createdTime);
        const fromDate = filters.dateRange.from ? new Date(filters.dateRange.from) : null;
        const toDate = filters.dateRange.to ? new Date(filters.dateRange.to) : null;
        
        if (fromDate && createdDate < fromDate) {
          return false;
        }
        if (toDate && createdDate > toDate) {
          return false;
        }
      }
      
      return true;
    });
  }

  /**
   * 获取所有标签页
   */
  getAllTabs() {
    const allTabs = [];
    
    this.app.windowGroups.forEach(group => {
      group.tabs.forEach(tab => {
        const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
        allTabs.push({
          tab: tab,
          metadata: metadata,
          groupId: group.id
        });
      });
    });
    
    return allTabs;
  }

  /**
   * 清除所有筛选
   */
  clearAllFilters() {
    this.currentFilters = {
      query: '',
      category: '',
      tags: [],
      priority: null,
      rating: null,
      bookmarked: null,
      archived: null,
      dateRange: null,
      domain: ''
    };
    
    // 清空UI
    const panel = document.querySelector('.advanced-search-panel');
    if (panel) {
      panel.querySelectorAll('input, select').forEach(input => {
        if (input.type === 'checkbox') {
          input.checked = false;
        } else {
          input.value = '';
        }
      });
    }
    
    // 清空基础搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.value = '';
    }
    
    this.updateTagFilterUI();
    this.executeSearch();
  }

  /**
   * 构建搜索描述
   */
  buildSearchDescription() {
    const parts = [];
    
    if (this.currentFilters.query) {
      parts.push(`搜索: "${this.currentFilters.query}"`);
    }
    
    if (this.currentFilters.category) {
      const category = this.app.tabNotesManager.categories.get(this.currentFilters.category);
      if (category) {
        parts.push(`分类: ${category.name}`);
      }
    }
    
    if (this.currentFilters.tags.length > 0) {
      parts.push(`标签: ${this.currentFilters.tags.join(', ')}`);
    }
    
    if (this.currentFilters.priority) {
      parts.push(`优先级: ${this.currentFilters.priority}`);
    }
    
    if (this.currentFilters.rating) {
      parts.push(`评分: ${this.currentFilters.rating}星`);
    }
    
    return parts.join(' | ') || '所有标签页';
  }

  /**
   * 添加到搜索历史
   */
  addToSearchHistory(description) {
    if (!description.trim()) return;
    
    // 移除重复项
    const index = this.searchHistory.indexOf(description);
    if (index > -1) {
      this.searchHistory.splice(index, 1);
    }
    
    // 添加到开头
    this.searchHistory.unshift(description);
    
    // 限制历史记录数量
    if (this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
    }
    
    this.saveSearchHistory();
    this.updateSearchHistory();
  }

  /**
   * 更新搜索历史显示
   */
  updateSearchHistory() {
    const historyList = document.querySelector('#searchHistoryList');
    if (!historyList) return;

    historyList.innerHTML = this.searchHistory.map(item => `
      <div class="search-history-item" data-search="${item.replace(/"/g, '&quot;')}">
        ${item}
      </div>
    `).join('');
    
    // 绑定点击事件
    historyList.querySelectorAll('.search-history-item').forEach(item => {
      item.addEventListener('click', (e) => {
        const searchText = e.target.dataset.search;
        this.loadSearchFromHistory(searchText);
      });
    });
  }

  /**
   * 从历史记录加载搜索
   */
  loadSearchFromHistory(description) {
    // 这里可以实现更复杂的历史记录恢复逻辑
    // 简化版本：将描述设置为查询
    this.currentFilters.query = description;
    
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.value = description;
    }
    
    this.executeSearch();
  }

  /**
   * 保存搜索历史
   */
  async saveSearchHistory() {
    try {
      await chrome.storage.local.set({
        searchHistory: this.searchHistory
      });
    } catch (error) {
      console.error('保存搜索历史失败:', error);
    }
  }

  /**
   * 加载搜索历史
   */
  async loadSearchHistory() {
    try {
      const result = await chrome.storage.local.get(['searchHistory']);
      if (result.searchHistory) {
        this.searchHistory = result.searchHistory;
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error);
    }
  }

  /**
   * 获取搜索统计
   */
  getSearchStats() {
    const results = this.searchTabs(this.currentFilters);
    
    return {
      totalResults: results.length,
      categories: this.getResultsByCategory(results),
      tags: this.getResultsByTags(results),
      ratings: this.getResultsByRating(results),
      domains: this.getResultsByDomain(results)
    };
  }

  /**
   * 按分类统计结果
   */
  getResultsByCategory(results) {
    const categories = {};
    results.forEach(result => {
      const category = result.metadata.category || 'uncategorized';
      categories[category] = (categories[category] || 0) + 1;
    });
    return categories;
  }

  /**
   * 按标签统计结果
   */
  getResultsByTags(results) {
    const tags = {};
    results.forEach(result => {
      result.metadata.tags.forEach(tag => {
        tags[tag] = (tags[tag] || 0) + 1;
      });
    });
    return tags;
  }

  /**
   * 按评分统计结果
   */
  getResultsByRating(results) {
    const ratings = {};
    results.forEach(result => {
      const rating = result.metadata.rating;
      ratings[rating] = (ratings[rating] || 0) + 1;
    });
    return ratings;
  }

  /**
   * 按域名统计结果
   */
  getResultsByDomain(results) {
    const domains = {};
    results.forEach(result => {
      try {
        const domain = new URL(result.tab.url).hostname;
        domains[domain] = (domains[domain] || 0) + 1;
      } catch (e) {
        domains['unknown'] = (domains['unknown'] || 0) + 1;
      }
    });
    return domains;
  }

  /**
   * 清理资源
   */
  cleanup() {
    const panel = document.querySelector('.advanced-search-panel');
    if (panel) {
      panel.remove();
    }
    
    this.searchHistory = [];
    this.currentFilters = {
      query: '',
      category: '',
      tags: [],
      priority: null,
      rating: null,
      bookmarked: null,
      archived: null,
      dateRange: null,
      domain: ''
    };
  }
}

export default SearchFilterManager;