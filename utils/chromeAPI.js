/**
 * Chrome API 封装类
 * 提供对chrome.tabs和chrome.windows API的统一封装
 */
class ChromeAPIWrapper {
  constructor() {
    this.isServiceWorker = typeof importScripts === 'function';
  }

  /**
   * 获取所有浏览器窗口
   */
  async getAllWindows() {
    try {
      const windows = await chrome.windows.getAll({
        populate: true,
        windowTypes: ['normal']
      });
      return windows;
    } catch (error) {
      console.error('获取窗口失败:', error);
      return [];
    }
  }

  /**
   * 获取所有标签页（按窗口分组）
   */
  async getAllTabsGroupedByWindow() {
    try {
      const windows = await this.getAllWindows();
      const groupedTabs = [];

      for (const window of windows) {
        const windowGroup = {
          id: window.id,
          windowId: window.id,
          title: `窗口 ${window.id}`,
          focused: window.focused,
          incognito: window.incognito,
          tabs: window.tabs || [],
          isCollapsed: false,
          order: groupedTabs.length,
          customName: null
        };
        groupedTabs.push(windowGroup);
      }

      return groupedTabs;
    } catch (error) {
      console.error('获取分组标签页失败:', error);
      return [];
    }
  }

  /**
   * 获取单个窗口的所有标签页
   */
  async getTabsInWindow(windowId) {
    try {
      const tabs = await chrome.tabs.query({ windowId });
      return tabs;
    } catch (error) {
      console.error(`获取窗口 ${windowId} 的标签页失败:`, error);
      return [];
    }
  }

  /**
   * 在新窗口中打开标签页
   */
  async openTabInNewWindow(url) {
    try {
      const window = await chrome.windows.create({
        url: url,
        focused: true
      });
      return window;
    } catch (error) {
      console.error('在新窗口打开标签页失败:', error);
      throw error;
    }
  }

  /**
   * 在当前窗口中打开标签页
   */
  async openTabInCurrentWindow(url, windowId = null) {
    try {
      const targetWindowId = windowId || (await this.getCurrentWindow()).id;
      const tab = await chrome.tabs.create({
        url: url,
        windowId: targetWindowId,
        active: true
      });
      return tab;
    } catch (error) {
      console.error('在当前窗口打开标签页失败:', error);
      throw error;
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return true;
    } catch (error) {
      console.error(`关闭标签页 ${tabId} 失败:`, error);
      return false;
    }
  }

  /**
   * 关闭多个标签页
   */
  async closeTabs(tabIds) {
    try {
      await chrome.tabs.remove(tabIds);
      return true;
    } catch (error) {
      console.error('批量关闭标签页失败:', error);
      return false;
    }
  }

  /**
   * 关闭整个窗口
   */
  async closeWindow(windowId) {
    try {
      await chrome.windows.remove(windowId);
      return true;
    } catch (error) {
      console.error(`关闭窗口 ${windowId} 失败:`, error);
      return false;
    }
  }

  /**
   * 激活标签页
   */
  async activateTab(tabId) {
    try {
      await chrome.tabs.update(tabId, { active: true });
      const tab = await chrome.tabs.get(tabId);
      await chrome.windows.update(tab.windowId, { focused: true });
      return true;
    } catch (error) {
      console.error(`激活标签页 ${tabId} 失败:`, error);
      return false;
    }
  }

  /**
   * 移动标签页到指定窗口
   */
  async moveTabToWindow(tabId, targetWindowId, index = -1) {
    try {
      await chrome.tabs.move(tabId, {
        windowId: targetWindowId,
        index: index
      });
      return true;
    } catch (error) {
      console.error(`移动标签页失败:`, error);
      return false;
    }
  }

  /**
   * 获取当前活动窗口
   */
  async getCurrentWindow() {
    try {
      return await chrome.windows.getCurrent();
    } catch (error) {
      console.error('获取当前窗口失败:', error);
      return null;
    }
  }

  /**
   * 更新窗口属性
   */
  async updateWindow(windowId, updateInfo) {
    try {
      return await chrome.windows.update(windowId, updateInfo);
    } catch (error) {
      console.error(`更新窗口 ${windowId} 失败:`, error);
      return null;
    }
  }

  /**
   * 监听标签页变化事件
   */
  addTabChangeListener(callback) {
    if (chrome.tabs.onCreated) {
      chrome.tabs.onCreated.addListener(callback);
    }
    if (chrome.tabs.onRemoved) {
      chrome.tabs.onRemoved.addListener(callback);
    }
    if (chrome.tabs.onUpdated) {
      chrome.tabs.onUpdated.addListener(callback);
    }
    if (chrome.tabs.onMoved) {
      chrome.tabs.onMoved.addListener(callback);
    }
  }

  /**
   * 监听窗口变化事件
   */
  addWindowChangeListener(callback) {
    if (chrome.windows.onCreated) {
      chrome.windows.onCreated.addListener(callback);
    }
    if (chrome.windows.onRemoved) {
      chrome.windows.onRemoved.addListener(callback);
    }
    if (chrome.windows.onFocusChanged) {
      chrome.windows.onFocusChanged.addListener(callback);
    }
  }

  /**
   * 获取标签页详细信息
   */
  async getTabInfo(tabId) {
    try {
      return await chrome.tabs.get(tabId);
    } catch (error) {
      console.error(`获取标签页 ${tabId} 信息失败:`, error);
      return null;
    }
  }

  /**
   * 搜索标签页
   */
  async searchTabs(query) {
    try {
      const allTabGroups = await this.getAllTabsGroupedByWindow();
      const results = [];

      for (const group of allTabGroups) {
        const matchingTabs = group.tabs.filter(tab => 
          tab.title.toLowerCase().includes(query.toLowerCase()) ||
          tab.url.toLowerCase().includes(query.toLowerCase())
        );
        
        if (matchingTabs.length > 0) {
          results.push({
            ...group,
            tabs: matchingTabs
          });
        }
      }

      return results;
    } catch (error) {
      console.error('搜索标签页失败:', error);
      return [];
    }
  }
}

// 导出API实例
const chromeAPI = new ChromeAPIWrapper();

// 在service worker中全局可用
if (typeof globalThis !== 'undefined') {
  globalThis.chromeAPI = chromeAPI;
}

// 为Chrome扩展环境提供全局访问
if (typeof window !== 'undefined') {
  window.ChromeAPIWrapper = ChromeAPIWrapper;
  window.chromeAPI = chromeAPI;
}

// export default ChromeAPIWrapper; // 注释掉ES6模块导出，改用全局变量