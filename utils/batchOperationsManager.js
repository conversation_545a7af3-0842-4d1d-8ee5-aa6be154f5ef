/**
 * 增强批量操作管理器
 * 提供更丰富的批量操作功能，包括智能筛选、条件操作、批量编辑等
 */
class BatchOperationsManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.selectedTabs = new Set();
    this.batchHistory = [];
    this.maxHistorySize = 50;
    this.operationPresets = new Map();
    this.loadPresets();
    this.initializeBatchOperations();
  }

  /**
   * 初始化批量操作功能
   */
  initializeBatchOperations() {
    this.createBatchOperationUI();
    this.bindBatchOperationEvents();
  }

  /**
   * 创建批量操作UI
   */
  createBatchOperationUI() {
    // 检查是否已经存在批量操作按钮
    const existingBtn = document.querySelector('.batch-operations-btn');
    if (existingBtn) return;

    // 在工具栏添加批量操作按钮
    const toolbarRight = document.querySelector('.toolbar-right');
    if (!toolbarRight) return;

    const batchOperationsBtn = document.createElement('button');
    batchOperationsBtn.className = 'btn btn-icon batch-operations-btn';
    batchOperationsBtn.innerHTML = '<span class="icon">🔧</span>';
    batchOperationsBtn.title = '批量操作';
    batchOperationsBtn.addEventListener('click', () => this.showBatchOperationsPanel());

    toolbarRight.appendChild(batchOperationsBtn);
  }

  /**
   * 显示批量操作面板
   */
  showBatchOperationsPanel() {
    const panel = document.createElement('div');
    panel.className = 'batch-operations-panel';
    panel.innerHTML = `
      <div class="panel-backdrop"></div>
      <div class="panel-content">
        <div class="panel-header">
          <h3>批量操作</h3>
          <button class="btn btn-icon close-panel">✕</button>
        </div>
        
        <div class="panel-body">
          <div class="batch-tabs">
            <button class="batch-tab active" data-tab="selection">选择</button>
            <button class="batch-tab" data-tab="operations">操作</button>
            <button class="batch-tab" data-tab="presets">预设</button>
            <button class="batch-tab" data-tab="history">历史</button>
          </div>
          
          <div class="tab-content active" id="selection-content">
            <div class="selection-section">
              <h4>智能选择</h4>
              <div class="selection-controls">
                <div class="selection-group">
                  <label>按条件选择</label>
                  <div class="condition-controls">
                    <select class="condition-type">
                      <option value="title">标题包含</option>
                      <option value="url">URL包含</option>
                      <option value="domain">域名匹配</option>
                      <option value="category">分类</option>
                      <option value="tags">标签</option>
                      <option value="status">状态</option>
                      <option value="memory">内存使用</option>
                      <option value="time">打开时间</option>
                    </select>
                    <input type="text" class="condition-value" placeholder="输入条件值">
                    <button class="btn btn-primary" id="selectByCondition">选择</button>
                  </div>
                </div>
                
                <div class="selection-group">
                  <label>快速选择</label>
                  <div class="quick-selection">
                    <button class="btn btn-small" data-action="select-all">全选</button>
                    <button class="btn btn-small" data-action="select-none">取消全选</button>
                    <button class="btn btn-small" data-action="select-active">当前标签页</button>
                    <button class="btn btn-small" data-action="select-inactive">非当前标签页</button>
                    <button class="btn btn-small" data-action="select-duplicates">重复标签页</button>
                    <button class="btn btn-small" data-action="select-unloaded">未加载标签页</button>
                    <button class="btn btn-small" data-action="select-old">长时间未访问</button>
                  </div>
                </div>
                
                <div class="selection-group">
                  <label>按窗口选择</label>
                  <div class="window-selection">
                    ${this.renderWindowSelection()}
                  </div>
                </div>
                
                <div class="selection-stats">
                  <span class="selected-count">已选择: ${this.selectedTabs.size}</span>
                  <span class="total-count">总计: ${this.getTotalTabCount()}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="operations-content">
            <div class="operations-section">
              <h4>批量操作</h4>
              <div class="operations-grid">
                <div class="operation-category">
                  <h5>基础操作</h5>
                  <div class="operation-buttons">
                    <button class="btn operation-btn" data-operation="close">
                      <span class="icon">✕</span>
                      <span>关闭标签页</span>
                    </button>
                    <button class="btn operation-btn" data-operation="duplicate">
                      <span class="icon">📋</span>
                      <span>复制标签页</span>
                    </button>
                    <button class="btn operation-btn" data-operation="pin">
                      <span class="icon">📌</span>
                      <span>固定标签页</span>
                    </button>
                    <button class="btn operation-btn" data-operation="unpin">
                      <span class="icon">📌</span>
                      <span>取消固定</span>
                    </button>
                  </div>
                </div>
                
                <div class="operation-category">
                  <h5>窗口操作</h5>
                  <div class="operation-buttons">
                    <button class="btn operation-btn" data-operation="new-window">
                      <span class="icon">🔗</span>
                      <span>新窗口打开</span>
                    </button>
                    <button class="btn operation-btn" data-operation="move-window">
                      <span class="icon">🔄</span>
                      <span>移动到窗口</span>
                    </button>
                    <button class="btn operation-btn" data-operation="group-window">
                      <span class="icon">📑</span>
                      <span>按窗口分组</span>
                    </button>
                  </div>
                </div>
                
                <div class="operation-category">
                  <h5>元数据操作</h5>
                  <div class="operation-buttons">
                    <button class="btn operation-btn" data-operation="batch-edit">
                      <span class="icon">✏️</span>
                      <span>批量编辑</span>
                    </button>
                    <button class="btn operation-btn" data-operation="add-tags">
                      <span class="icon">🏷️</span>
                      <span>添加标签</span>
                    </button>
                    <button class="btn operation-btn" data-operation="set-category">
                      <span class="icon">📂</span>
                      <span>设置分类</span>
                    </button>
                    <button class="btn operation-btn" data-operation="bookmark">
                      <span class="icon">⭐</span>
                      <span>加入书签</span>
                    </button>
                  </div>
                </div>
                
                <div class="operation-category">
                  <h5>高级操作</h5>
                  <div class="operation-buttons">
                    <button class="btn operation-btn" data-operation="sort">
                      <span class="icon">🔢</span>
                      <span>排序</span>
                    </button>
                    <button class="btn operation-btn" data-operation="merge-duplicates">
                      <span class="icon">🔄</span>
                      <span>合并重复</span>
                    </button>
                    <button class="btn operation-btn" data-operation="archive">
                      <span class="icon">📦</span>
                      <span>归档</span>
                    </button>
                    <button class="btn operation-btn" data-operation="export">
                      <span class="icon">📤</span>
                      <span>导出</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="presets-content">
            <div class="presets-section">
              <h4>操作预设</h4>
              <div class="presets-list">
                ${this.renderPresetsList()}
              </div>
              <div class="presets-actions">
                <button class="btn btn-primary" id="createPreset">
                  <span class="icon">➕</span> 创建新预设
                </button>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="history-content">
            <div class="history-section">
              <h4>操作历史</h4>
              <div class="history-list">
                ${this.renderHistoryList()}
              </div>
              <div class="history-actions">
                <button class="btn btn-secondary" id="clearHistory">清空历史</button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="panel-footer">
          <button class="btn btn-secondary" id="cancelBatch">取消</button>
          <button class="btn btn-primary" id="executeBatch">执行批量操作</button>
        </div>
      </div>
    `;

    document.body.appendChild(panel);
    this.bindPanelEvents(panel);
  }

  /**
   * 渲染窗口选择
   */
  renderWindowSelection() {
    return this.app.windowGroups.map(group => `
      <label class="checkbox-label">
        <input type="checkbox" class="window-checkbox" value="${group.id}">
        <span>${group.title} (${group.tabs.length})</span>
      </label>
    `).join('');
  }

  /**
   * 渲染预设列表
   */
  renderPresetsList() {
    if (this.operationPresets.size === 0) {
      return '<div class="empty-presets">还没有创建任何预设</div>';
    }

    return Array.from(this.operationPresets.values()).map(preset => `
      <div class="preset-item" data-preset-id="${preset.id}">
        <div class="preset-info">
          <div class="preset-name">${preset.name}</div>
          <div class="preset-description">${preset.description}</div>
          <div class="preset-operations">
            ${preset.operations.map(op => `<span class="operation-tag">${op.type}</span>`).join('')}
          </div>
        </div>
        <div class="preset-actions">
          <button class="btn btn-small" data-action="run-preset" data-preset-id="${preset.id}">
            <span class="icon">▶️</span>
          </button>
          <button class="btn btn-small" data-action="edit-preset" data-preset-id="${preset.id}">
            <span class="icon">✏️</span>
          </button>
          <button class="btn btn-small" data-action="delete-preset" data-preset-id="${preset.id}">
            <span class="icon">🗑️</span>
          </button>
        </div>
      </div>
    `).join('');
  }

  /**
   * 渲染历史列表
   */
  renderHistoryList() {
    if (this.batchHistory.length === 0) {
      return '<div class="empty-history">没有操作历史</div>';
    }

    return this.batchHistory.slice(-10).reverse().map(entry => `
      <div class="history-item">
        <div class="history-info">
          <div class="history-operation">${entry.operation}</div>
          <div class="history-time">${new Date(entry.timestamp).toLocaleString()}</div>
          <div class="history-details">
            影响了 ${entry.affectedTabs} 个标签页
          </div>
        </div>
        <div class="history-actions">
          <button class="btn btn-small" data-action="undo-operation" data-entry-id="${entry.id}">
            <span class="icon">↩️</span>
          </button>
        </div>
      </div>
    `).join('');
  }

  /**
   * 绑定面板事件
   */
  bindPanelEvents(panel) {
    // 关闭面板
    const closePanel = () => {
      panel.remove();
    };

    panel.querySelector('.close-panel').addEventListener('click', closePanel);
    panel.querySelector('.panel-backdrop').addEventListener('click', closePanel);
    panel.querySelector('#cancelBatch').addEventListener('click', closePanel);

    // 标签页切换
    const tabBtns = panel.querySelectorAll('.batch-tab');
    const tabContents = panel.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const tab = btn.dataset.tab;
        
        tabBtns.forEach(b => b.classList.remove('active'));
        tabContents.forEach(c => c.classList.remove('active'));
        
        btn.classList.add('active');
        panel.querySelector(`#${tab}-content`).classList.add('active');
      });
    });

    // 按条件选择
    panel.querySelector('#selectByCondition').addEventListener('click', () => {
      const conditionType = panel.querySelector('.condition-type').value;
      const conditionValue = panel.querySelector('.condition-value').value;
      this.selectByCondition(conditionType, conditionValue);
      this.updateSelectionStats(panel);
    });

    // 快速选择
    panel.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      if (action && action.startsWith('select-')) {
        this.handleQuickSelection(action);
        this.updateSelectionStats(panel);
      }
    });

    // 窗口选择
    panel.querySelectorAll('.window-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.selectTabsFromWindow(parseInt(checkbox.value), checkbox.checked);
        this.updateSelectionStats(panel);
      });
    });

    // 批量操作
    panel.addEventListener('click', (e) => {
      const operation = e.target.closest('[data-operation]')?.dataset.operation;
      if (operation) {
        this.handleBatchOperation(operation);
      }
    });

    // 预设操作
    panel.addEventListener('click', (e) => {
      const presetAction = e.target.closest('[data-action]')?.dataset.action;
      const presetId = e.target.closest('[data-preset-id]')?.dataset.presetId;
      
      if (presetAction && presetId) {
        this.handlePresetAction(presetAction, presetId);
      }
    });

    // 创建预设
    panel.querySelector('#createPreset').addEventListener('click', () => {
      this.showCreatePresetDialog();
    });

    // 清空历史
    panel.querySelector('#clearHistory').addEventListener('click', () => {
      this.clearBatchHistory();
      this.refreshHistoryList(panel);
    });

    // 执行批量操作
    panel.querySelector('#executeBatch').addEventListener('click', () => {
      this.executeBatchOperations();
    });
  }

  /**
   * 按条件选择标签页
   */
  selectByCondition(conditionType, conditionValue) {
    this.selectedTabs.clear();
    
    const allTabs = this.getAllTabs();
    const filteredTabs = allTabs.filter(tabInfo => {
      const tab = tabInfo.tab;
      const metadata = tabInfo.metadata;
      
      switch (conditionType) {
        case 'title':
          return tab.title.toLowerCase().includes(conditionValue.toLowerCase());
        case 'url':
          return tab.url.toLowerCase().includes(conditionValue.toLowerCase());
        case 'domain':
          return this.extractDomain(tab.url).includes(conditionValue.toLowerCase());
        case 'category':
          return metadata.category === conditionValue;
        case 'tags':
          return metadata.tags.some(tag => tag.toLowerCase().includes(conditionValue.toLowerCase()));
        case 'status':
          return tab.status === conditionValue;
        case 'memory':
          // 简单的内存使用估算
          return this.estimateMemoryUsage(tab) > parseInt(conditionValue);
        case 'time':
          // 按打开时间筛选（小时）
          const hours = parseInt(conditionValue);
          return this.getTabOpenTime(tab) > hours * 3600000;
        default:
          return false;
      }
    });

    filteredTabs.forEach(tabInfo => {
      this.selectedTabs.add(tabInfo.tab.id);
    });
  }

  /**
   * 处理快速选择
   */
  handleQuickSelection(action) {
    const allTabs = this.getAllTabs();
    
    switch (action) {
      case 'select-all':
        this.selectedTabs.clear();
        allTabs.forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
      case 'select-none':
        this.selectedTabs.clear();
        break;
      case 'select-active':
        this.selectedTabs.clear();
        allTabs.filter(tabInfo => tabInfo.tab.active)
          .forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
      case 'select-inactive':
        this.selectedTabs.clear();
        allTabs.filter(tabInfo => !tabInfo.tab.active)
          .forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
      case 'select-duplicates':
        this.selectedTabs.clear();
        const duplicates = this.findDuplicateTabs(allTabs);
        duplicates.forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
      case 'select-unloaded':
        this.selectedTabs.clear();
        allTabs.filter(tabInfo => tabInfo.tab.status !== 'complete')
          .forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
      case 'select-old':
        this.selectedTabs.clear();
        // 选择超过1小时未访问的标签页
        allTabs.filter(tabInfo => this.getTabOpenTime(tabInfo.tab) > 3600000)
          .forEach(tabInfo => this.selectedTabs.add(tabInfo.tab.id));
        break;
    }
  }

  /**
   * 从窗口选择标签页
   */
  selectTabsFromWindow(windowId, selected) {
    const windowGroup = this.app.windowGroups.find(group => group.id === windowId);
    if (!windowGroup) return;

    if (selected) {
      windowGroup.tabs.forEach(tab => this.selectedTabs.add(tab.id));
    } else {
      windowGroup.tabs.forEach(tab => this.selectedTabs.delete(tab.id));
    }
  }

  /**
   * 处理批量操作
   */
  async handleBatchOperation(operation) {
    if (this.selectedTabs.size === 0) {
      this.app.setStatus('请先选择要操作的标签页');
      return;
    }

    const selectedTabIds = Array.from(this.selectedTabs);
    const affectedTabs = selectedTabIds.length;
    
    try {
      switch (operation) {
        case 'close':
          await this.batchCloseTab(selectedTabIds);
          break;
        case 'duplicate':
          await this.batchDuplicateTab(selectedTabIds);
          break;
        case 'pin':
          await this.batchPinTab(selectedTabIds, true);
          break;
        case 'unpin':
          await this.batchPinTab(selectedTabIds, false);
          break;
        case 'new-window':
          await this.batchOpenInNewWindow(selectedTabIds);
          break;
        case 'move-window':
          await this.showMoveToWindowDialog(selectedTabIds);
          break;
        case 'batch-edit':
          await this.showBatchEditDialog(selectedTabIds);
          break;
        case 'add-tags':
          await this.showAddTagsDialog(selectedTabIds);
          break;
        case 'set-category':
          await this.showSetCategoryDialog(selectedTabIds);
          break;
        case 'bookmark':
          await this.batchBookmarkTab(selectedTabIds);
          break;
        case 'sort':
          await this.showSortDialog(selectedTabIds);
          break;
        case 'merge-duplicates':
          await this.batchMergeDuplicates();
          break;
        case 'archive':
          await this.batchArchiveTab(selectedTabIds);
          break;
        case 'export':
          await this.batchExportTab(selectedTabIds);
          break;
        default:
          this.app.setStatus('未知的批量操作');
          return;
      }

      // 记录操作历史
      this.addToHistory({
        operation: operation,
        affectedTabs: affectedTabs,
        timestamp: Date.now()
      });

      this.app.setStatus(`批量操作完成，影响了 ${affectedTabs} 个标签页`);
      
    } catch (error) {
      console.error('批量操作失败:', error);
      this.app.setStatus('批量操作失败');
    }
  }

  /**
   * 批量关闭标签页
   */
  async batchCloseTab(tabIds) {
    await this.app.sendMessage('CLOSE_TABS', { tabIds });
    await this.app.refreshData();
  }

  /**
   * 批量复制标签页
   */
  async batchDuplicateTab(tabIds) {
    for (const tabId of tabIds) {
      const tab = this.app.findTabById(tabId);
      if (tab) {
        await this.app.sendMessage('OPEN_TAB_CURRENT_WINDOW', { 
          url: tab.url, 
          windowId: tab.windowId 
        });
      }
    }
    await this.app.refreshData();
  }

  /**
   * 批量固定/取消固定标签页
   */
  async batchPinTab(tabIds, pinned) {
    // 这里需要扩展Chrome API来支持固定标签页
    // 目前作为占位符实现
    this.app.setStatus(`已${pinned ? '固定' : '取消固定'} ${tabIds.length} 个标签页`);
  }

  /**
   * 批量在新窗口打开
   */
  async batchOpenInNewWindow(tabIds) {
    for (const tabId of tabIds) {
      const tab = this.app.findTabById(tabId);
      if (tab) {
        await this.app.sendMessage('OPEN_TAB_NEW_WINDOW', { url: tab.url });
      }
    }
  }

  /**
   * 批量加入书签
   */
  async batchBookmarkTab(tabIds) {
    for (const tabId of tabIds) {
      const tab = this.app.findTabById(tabId);
      if (tab) {
        const metadata = this.app.tabNotesManager.getTabMetadata(tabId);
        metadata.isBookmarked = true;
        await this.app.tabNotesManager.saveTabMetadata(tabId, metadata);
      }
    }
    await this.app.refreshData();
  }

  /**
   * 批量归档标签页
   */
  async batchArchiveTab(tabIds) {
    for (const tabId of tabIds) {
      const metadata = this.app.tabNotesManager.getTabMetadata(tabId);
      metadata.isArchived = true;
      metadata.archivedTime = Date.now();
      await this.app.tabNotesManager.saveTabMetadata(tabId, metadata);
    }
    await this.app.refreshData();
  }

  /**
   * 批量导出标签页
   */
  async batchExportTab(tabIds) {
    const tabsData = [];
    
    for (const tabId of tabIds) {
      const tab = this.app.findTabById(tabId);
      if (tab) {
        const metadata = this.app.tabNotesManager.getTabMetadata(tabId);
        tabsData.push({
          title: tab.title,
          url: tab.url,
          metadata: metadata
        });
      }
    }

    const exportData = {
      exportTime: new Date().toISOString(),
      tabsCount: tabsData.length,
      tabs: tabsData
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `batch-export-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  /**
   * 更新选择统计信息
   */
  updateSelectionStats(panel) {
    const selectedCount = panel.querySelector('.selected-count');
    const totalCount = panel.querySelector('.total-count');
    
    if (selectedCount) {
      selectedCount.textContent = `已选择: ${this.selectedTabs.size}`;
    }
    
    if (totalCount) {
      totalCount.textContent = `总计: ${this.getTotalTabCount()}`;
    }
  }

  /**
   * 获取所有标签页
   */
  getAllTabs() {
    const allTabs = [];
    this.app.windowGroups.forEach(group => {
      group.tabs.forEach(tab => {
        const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
        allTabs.push({ tab, metadata });
      });
    });
    return allTabs;
  }

  /**
   * 获取总标签页数量
   */
  getTotalTabCount() {
    return this.app.windowGroups.reduce((total, group) => total + group.tabs.length, 0);
  }

  /**
   * 查找重复标签页
   */
  findDuplicateTabs(allTabs) {
    const urlMap = new Map();
    const duplicates = [];

    allTabs.forEach(tabInfo => {
      const url = tabInfo.tab.url;
      if (urlMap.has(url)) {
        duplicates.push(tabInfo);
      } else {
        urlMap.set(url, tabInfo);
      }
    });

    return duplicates;
  }

  /**
   * 提取域名
   */
  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * 估算内存使用
   */
  estimateMemoryUsage(tab) {
    // 简单的内存使用估算，实际实现需要更复杂的逻辑
    const baseMemory = 50; // MB
    const titleFactor = tab.title.length * 0.1;
    const urlFactor = tab.url.length * 0.05;
    return baseMemory + titleFactor + urlFactor;
  }

  /**
   * 获取标签页打开时间
   */
  getTabOpenTime(tab) {
    // 这里需要更复杂的逻辑来跟踪标签页打开时间
    // 目前返回随机值作为示例
    return Math.random() * 7200000; // 0-2小时
  }

  /**
   * 添加到历史记录
   */
  addToHistory(entry) {
    entry.id = Date.now();
    this.batchHistory.push(entry);
    
    // 保持历史记录大小
    if (this.batchHistory.length > this.maxHistorySize) {
      this.batchHistory.shift();
    }
    
    this.saveBatchHistory();
  }

  /**
   * 清空批量历史
   */
  clearBatchHistory() {
    this.batchHistory = [];
    this.saveBatchHistory();
  }

  /**
   * 保存批量历史
   */
  async saveBatchHistory() {
    try {
      await chrome.storage.local.set({ batchHistory: this.batchHistory });
    } catch (error) {
      console.error('保存批量历史失败:', error);
    }
  }

  /**
   * 加载批量历史
   */
  async loadBatchHistory() {
    try {
      const data = await chrome.storage.local.get(['batchHistory']);
      if (data.batchHistory) {
        this.batchHistory = data.batchHistory;
      }
    } catch (error) {
      console.error('加载批量历史失败:', error);
    }
  }

  /**
   * 加载预设
   */
  async loadPresets() {
    try {
      const data = await chrome.storage.local.get(['operationPresets']);
      if (data.operationPresets) {
        this.operationPresets = new Map(Object.entries(data.operationPresets));
      }
    } catch (error) {
      console.error('加载预设失败:', error);
    }
  }

  /**
   * 保存预设
   */
  async savePresets() {
    try {
      const presetsData = {};
      for (const [id, preset] of this.operationPresets) {
        presetsData[id] = preset;
      }
      await chrome.storage.local.set({ operationPresets: presetsData });
    } catch (error) {
      console.error('保存预设失败:', error);
    }
  }

  /**
   * 刷新历史列表
   */
  refreshHistoryList(panel) {
    const historyList = panel.querySelector('.history-list');
    if (historyList) {
      historyList.innerHTML = this.renderHistoryList();
    }
  }

  /**
   * 执行批量操作
   */
  executeBatchOperations() {
    // 这里可以添加确认对话框
    if (this.selectedTabs.size === 0) {
      this.app.setStatus('请先选择要操作的标签页');
      return;
    }

    const confirmed = confirm(`确定要对选中的 ${this.selectedTabs.size} 个标签页执行批量操作吗？`);
    if (confirmed) {
      this.app.setStatus('批量操作已执行');
      // 这里可以添加具体的执行逻辑
    }
  }

  /**
   * 绑定批量操作事件
   */
  bindBatchOperationEvents() {
    // 监听标签页选择变化
    document.addEventListener('tab-selection-changed', (e) => {
      this.selectedTabs = new Set(e.detail.selectedTabs);
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    const batchOperationsBtn = document.querySelector('.batch-operations-btn');
    if (batchOperationsBtn) {
      batchOperationsBtn.remove();
    }
    
    const panel = document.querySelector('.batch-operations-panel');
    if (panel) {
      panel.remove();
    }
  }
}

export default BatchOperationsManager;