/**
 * 标签页备注和元数据管理系统
 * 为每个标签页提供备注、分类、标签等扩展功能
 */
class TabNotesManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.tabMetadata = new Map(); // 标签页元数据存储
    this.categories = new Map(); // 分类定义
    this.tags = new Set(); // 所有标签
    this.initializeDefaultCategories();
  }

  /**
   * 初始化默认分类
   */
  initializeDefaultCategories() {
    const defaultCategories = [
      { id: 'work', name: '工作', color: '#1a73e8', icon: '💼' },
      { id: 'study', name: '学习', color: '#137333', icon: '📚' },
      { id: 'entertainment', name: '娱乐', color: '#d93025', icon: '🎮' },
      { id: 'shopping', name: '购物', color: '#f9ab00', icon: '🛒' },
      { id: 'news', name: '新闻', color: '#9334e6', icon: '📰' },
      { id: 'tools', name: '工具', color: '#0f766e', icon: '🔧' },
      { id: 'reference', name: '参考', color: '#7c2d12', icon: '📋' },
      { id: 'personal', name: '个人', color: '#be185d', icon: '👤' }
    ];

    defaultCategories.forEach(category => {
      this.categories.set(category.id, category);
    });
  }

  /**
   * 获取标签页元数据
   */
  getTabMetadata(tabId) {
    return this.tabMetadata.get(tabId) || this.createDefaultMetadata(tabId);
  }

  /**
   * 创建默认元数据
   */
  createDefaultMetadata(tabId) {
    const metadata = {
      tabId: tabId,
      notes: '',
      tags: [],
      category: null,
      priority: 3, // 1-5, 3为默认
      rating: 0, // 0-5
      color: null,
      isBookmarked: false,
      isArchived: false,
      createdTime: Date.now(),
      lastModified: Date.now(),
      visitCount: 0,
      lastVisitTime: Date.now()
    };
    
    this.tabMetadata.set(tabId, metadata);
    return metadata;
  }

  /**
   * 更新标签页备注
   */
  async updateTabNotes(tabId, notes) {
    const metadata = this.getTabMetadata(tabId);
    metadata.notes = notes;
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups(); // 重新渲染以显示更新
  }

  /**
   * 添加标签页标签
   */
  async addTabTag(tabId, tag) {
    const metadata = this.getTabMetadata(tabId);
    if (!metadata.tags.includes(tag)) {
      metadata.tags.push(tag);
      metadata.lastModified = Date.now();
      this.tags.add(tag);
      
      await this.saveTabMetadata(tabId, metadata);
      this.app.renderWindowGroups();
    }
  }

  /**
   * 移除标签页标签
   */
  async removeTabTag(tabId, tag) {
    const metadata = this.getTabMetadata(tabId);
    const index = metadata.tags.indexOf(tag);
    if (index > -1) {
      metadata.tags.splice(index, 1);
      metadata.lastModified = Date.now();
      
      await this.saveTabMetadata(tabId, metadata);
      this.app.renderWindowGroups();
    }
  }

  /**
   * 设置标签页分类
   */
  async setTabCategory(tabId, categoryId) {
    const metadata = this.getTabMetadata(tabId);
    metadata.category = categoryId;
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 设置标签页优先级
   */
  async setTabPriority(tabId, priority) {
    const metadata = this.getTabMetadata(tabId);
    metadata.priority = Math.max(1, Math.min(5, priority));
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 设置标签页评分
   */
  async setTabRating(tabId, rating) {
    const metadata = this.getTabMetadata(tabId);
    metadata.rating = Math.max(0, Math.min(5, rating));
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 切换标签页书签状态
   */
  async toggleTabBookmark(tabId) {
    const metadata = this.getTabMetadata(tabId);
    metadata.isBookmarked = !metadata.isBookmarked;
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 归档标签页
   */
  async archiveTab(tabId) {
    const metadata = this.getTabMetadata(tabId);
    metadata.isArchived = true;
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 保存标签页元数据
   */
  async saveTabMetadata(tabId, metadata) {
    this.tabMetadata.set(tabId, metadata);
    
    // 保存到持久化存储
    const allMetadata = {};
    this.tabMetadata.forEach((data, id) => {
      allMetadata[id] = data;
    });
    
    await chrome.storage.local.set({
      tabMetadata: allMetadata
    });
  }

  /**
   * 加载标签页元数据
   */
  async loadTabMetadata() {
    try {
      const result = await chrome.storage.local.get(['tabMetadata']);
      if (result.tabMetadata) {
        Object.entries(result.tabMetadata).forEach(([tabId, metadata]) => {
          this.tabMetadata.set(parseInt(tabId), metadata);
          
          // 收集所有标签
          if (metadata.tags) {
            metadata.tags.forEach(tag => this.tags.add(tag));
          }
        });
      }
    } catch (error) {
      console.error('加载标签页元数据失败:', error);
    }
  }

  /**
   * 创建标签页编辑界面
   */
  createTabEditDialog(tabId, tab) {
    const metadata = this.getTabMetadata(tabId);
    
    const dialog = document.createElement('div');
    dialog.className = 'tab-edit-dialog';
    dialog.innerHTML = `
      <div class="dialog-backdrop"></div>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>编辑标签页</h3>
          <button class="btn btn-icon dialog-close">✕</button>
        </div>
        <div class="dialog-body">
          <div class="tab-edit-preview">
            <img class="tab-favicon" src="${tab.favIconUrl || ''}" onerror="this.style.display='none'">
            <div class="tab-info">
              <div class="tab-title">${this.app.escapeHtml(tab.title)}</div>
              <div class="tab-url">${this.app.escapeHtml(tab.url)}</div>
            </div>
          </div>
          
          <div class="form-group">
            <label for="tabNotes">备注</label>
            <textarea id="tabNotes" class="form-control" placeholder="添加您的备注..." rows="3">${metadata.notes}</textarea>
          </div>
          
          <div class="form-group">
            <label>分类</label>
            <div class="category-grid">
              ${Array.from(this.categories.values()).map(cat => `
                <div class="category-item ${metadata.category === cat.id ? 'selected' : ''}" 
                     data-category-id="${cat.id}">
                  <div class="category-color" style="background-color: ${cat.color}"></div>
                  <span class="category-icon">${cat.icon}</span>
                  <span class="category-name">${cat.name}</span>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="form-group">
            <label for="tabTags">标签</label>
            <div class="tag-input-container">
              <input type="text" id="tabTags" class="form-control" placeholder="输入标签，按回车添加">
              <div class="tag-suggestions"></div>
            </div>
            <div class="tag-list">
              ${metadata.tags.map(tag => `
                <span class="tag-item">
                  ${tag}
                  <button class="tag-remove" data-tag="${tag}">×</button>
                </span>
              `).join('')}
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="tabPriority">优先级</label>
              <select id="tabPriority" class="form-control">
                <option value="1" ${metadata.priority === 1 ? 'selected' : ''}>⭐ 最低</option>
                <option value="2" ${metadata.priority === 2 ? 'selected' : ''}>⭐⭐ 低</option>
                <option value="3" ${metadata.priority === 3 ? 'selected' : ''}>⭐⭐⭐ 普通</option>
                <option value="4" ${metadata.priority === 4 ? 'selected' : ''}>⭐⭐⭐⭐ 高</option>
                <option value="5" ${metadata.priority === 5 ? 'selected' : ''}>⭐⭐⭐⭐⭐ 最高</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="tabRating">评分</label>
              <div class="rating-input">
                ${[1,2,3,4,5].map(i => `
                  <span class="rating-star ${i <= metadata.rating ? 'active' : ''}" 
                        data-rating="${i}">⭐</span>
                `).join('')}
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="tabBookmark" ${metadata.isBookmarked ? 'checked' : ''}>
              <span class="checkbox-text">📌 收藏此标签页</span>
            </label>
          </div>
        </div>
        
        <div class="dialog-footer">
          <button class="btn btn-secondary dialog-cancel">取消</button>
          <button class="btn btn-primary dialog-save">保存</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(dialog);
    this.bindTabEditEvents(dialog, tabId);
    
    // 聚焦备注输入框
    setTimeout(() => {
      dialog.querySelector('#tabNotes').focus();
    }, 100);
  }

  /**
   * 绑定标签页编辑事件
   */
  bindTabEditEvents(dialog, tabId) {
    const closeDialog = () => {
      dialog.remove();
    };

    // 关闭事件
    dialog.querySelector('.dialog-close').addEventListener('click', closeDialog);
    dialog.querySelector('.dialog-cancel').addEventListener('click', closeDialog);
    dialog.querySelector('.dialog-backdrop').addEventListener('click', closeDialog);

    // 分类选择
    dialog.querySelectorAll('.category-item').forEach(item => {
      item.addEventListener('click', () => {
        dialog.querySelectorAll('.category-item').forEach(i => i.classList.remove('selected'));
        item.classList.add('selected');
      });
    });

    // 标签输入
    const tagInput = dialog.querySelector('#tabTags');
    tagInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        const tag = tagInput.value.trim();
        if (tag) {
          this.addTagToDialog(dialog, tag);
          tagInput.value = '';
        }
      }
    });

    // 标签删除
    dialog.addEventListener('click', (e) => {
      if (e.target.classList.contains('tag-remove')) {
        e.target.parentElement.remove();
      }
    });

    // 评分选择
    dialog.querySelectorAll('.rating-star').forEach(star => {
      star.addEventListener('click', () => {
        const rating = parseInt(star.dataset.rating);
        dialog.querySelectorAll('.rating-star').forEach((s, i) => {
          s.classList.toggle('active', i < rating);
        });
      });
    });

    // 保存事件
    dialog.querySelector('.dialog-save').addEventListener('click', () => {
      this.saveTabEditDialog(dialog, tabId);
      closeDialog();
    });
  }

  /**
   * 添加标签到对话框
   */
  addTagToDialog(dialog, tag) {
    const tagList = dialog.querySelector('.tag-list');
    const tagItem = document.createElement('span');
    tagItem.className = 'tag-item';
    tagItem.innerHTML = `
      ${tag}
      <button class="tag-remove" data-tag="${tag}">×</button>
    `;
    tagList.appendChild(tagItem);
  }

  /**
   * 保存标签页编辑对话框
   */
  async saveTabEditDialog(dialog, tabId) {
    const metadata = this.getTabMetadata(tabId);
    
    // 保存备注
    metadata.notes = dialog.querySelector('#tabNotes').value;
    
    // 保存分类
    const selectedCategory = dialog.querySelector('.category-item.selected');
    metadata.category = selectedCategory ? selectedCategory.dataset.categoryId : null;
    
    // 保存标签
    metadata.tags = Array.from(dialog.querySelectorAll('.tag-item'))
      .map(item => item.textContent.trim().replace('×', ''));
    
    // 保存优先级
    metadata.priority = parseInt(dialog.querySelector('#tabPriority').value);
    
    // 保存评分
    metadata.rating = dialog.querySelectorAll('.rating-star.active').length;
    
    // 保存收藏状态
    metadata.isBookmarked = dialog.querySelector('#tabBookmark').checked;
    
    metadata.lastModified = Date.now();
    
    await this.saveTabMetadata(tabId, metadata);
    this.app.renderWindowGroups();
  }

  /**
   * 获取所有分类
   */
  getCategories() {
    return Array.from(this.categories.values());
  }

  /**
   * 获取所有标签
   */
  getTags() {
    return Array.from(this.tags);
  }

  /**
   * 搜索标签页
   */
  searchTabs(query, filters = {}) {
    const results = [];
    
    this.app.windowGroups.forEach(group => {
      group.tabs.forEach(tab => {
        const metadata = this.getTabMetadata(tab.id);
        let matches = true;
        
        // 文本搜索
        if (query) {
          const searchText = `${tab.title} ${tab.url} ${metadata.notes}`.toLowerCase();
          matches = matches && searchText.includes(query.toLowerCase());
        }
        
        // 分类筛选
        if (filters.category && metadata.category !== filters.category) {
          matches = false;
        }
        
        // 标签筛选
        if (filters.tags && filters.tags.length > 0) {
          const hasAllTags = filters.tags.every(tag => metadata.tags.includes(tag));
          matches = matches && hasAllTags;
        }
        
        // 优先级筛选
        if (filters.priority && metadata.priority !== filters.priority) {
          matches = false;
        }
        
        // 收藏筛选
        if (filters.bookmarked !== undefined && metadata.isBookmarked !== filters.bookmarked) {
          matches = false;
        }
        
        if (matches) {
          results.push({
            tab: tab,
            metadata: metadata,
            groupId: group.id
          });
        }
      });
    });
    
    return results;
  }

  /**
   * 清理无效的元数据
   */
  cleanupMetadata(validTabIds) {
    const toRemove = [];
    
    this.tabMetadata.forEach((metadata, tabId) => {
      if (!validTabIds.includes(tabId)) {
        toRemove.push(tabId);
      }
    });
    
    toRemove.forEach(tabId => {
      this.tabMetadata.delete(tabId);
    });
    
    if (toRemove.length > 0) {
      this.saveAllMetadata();
    }
  }

  /**
   * 保存所有元数据
   */
  async saveAllMetadata() {
    const allMetadata = {};
    this.tabMetadata.forEach((data, id) => {
      allMetadata[id] = data;
    });
    
    await chrome.storage.local.set({
      tabMetadata: allMetadata
    });
  }

  /**
   * 导出标签页数据
   */
  exportTabData() {
    const exportData = {
      tabs: [],
      categories: Array.from(this.categories.values()),
      tags: Array.from(this.tags),
      exportTime: new Date().toISOString()
    };
    
    this.app.windowGroups.forEach(group => {
      group.tabs.forEach(tab => {
        const metadata = this.getTabMetadata(tab.id);
        exportData.tabs.push({
          title: tab.title,
          url: tab.url,
          favIconUrl: tab.favIconUrl,
          windowId: tab.windowId,
          metadata: metadata
        });
      });
    });
    
    return exportData;
  }

  /**
   * 导入标签页数据
   */
  async importTabData(importData) {
    try {
      if (importData.categories) {
        importData.categories.forEach(category => {
          this.categories.set(category.id, category);
        });
      }
      
      if (importData.tags) {
        importData.tags.forEach(tag => {
          this.tags.add(tag);
        });
      }
      
      if (importData.tabs) {
        importData.tabs.forEach(tabData => {
          if (tabData.metadata) {
            // 这里需要根据URL匹配现有标签页
            const existingTab = this.findTabByUrl(tabData.url);
            if (existingTab) {
              this.tabMetadata.set(existingTab.id, {
                ...tabData.metadata,
                tabId: existingTab.id
              });
            }
          }
        });
      }
      
      await this.saveAllMetadata();
      return true;
    } catch (error) {
      console.error('导入标签页数据失败:', error);
      return false;
    }
  }

  /**
   * 根据URL查找标签页
   */
  findTabByUrl(url) {
    for (const group of this.app.windowGroups) {
      for (const tab of group.tabs) {
        if (tab.url === url) {
          return tab;
        }
      }
    }
    return null;
  }
}

export default TabNotesManager;