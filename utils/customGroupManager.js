/**
 * 自定义分组管理器
 * 允许用户创建自定义的标签页分组，提供更灵活的组织方式
 */
class CustomGroupManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.customGroups = new Map();
    this.groupColors = [
      '#1a73e8', '#34a853', '#ea4335', '#fbbc04', '#9c27b0',
      '#ff9800', '#607d8b', '#795548', '#e91e63', '#00bcd4'
    ];
    this.groupIcons = [
      '📁', '🏷️', '⭐', '🎯', '🏆', '🔥', '💡', '🎨', '🔧', '📊',
      '🎮', '💼', '🎓', '🍕', '🌟', '🎪', '🚀', '🎭', '🎪', '🎨'
    ];
    
    this.initializeCustomGroups();
  }

  /**
   * 初始化自定义分组功能
   */
  async initializeCustomGroups() {
    try {
      await this.loadCustomGroups();
      this.createCustomGroupUI();
      this.bindCustomGroupEvents();
    } catch (error) {
      console.error('初始化自定义分组失败:', error);
    }
  }

  /**
   * 创建自定义分组UI
   */
  createCustomGroupUI() {
    // 检查是否已经存在自定义分组按钮
    const existingBtn = document.querySelector('.custom-group-btn');
    if (existingBtn) return;

    // 在工具栏添加自定义分组按钮
    const toolbarRight = document.querySelector('.toolbar-right');
    if (!toolbarRight) return;

    const customGroupBtn = document.createElement('button');
    customGroupBtn.className = 'btn btn-icon custom-group-btn';
    customGroupBtn.innerHTML = '<span class="icon">📁</span>';
    customGroupBtn.title = '自定义分组';
    customGroupBtn.addEventListener('click', () => this.showCustomGroupPanel());

    toolbarRight.appendChild(customGroupBtn);
  }

  /**
   * 显示自定义分组面板
   */
  showCustomGroupPanel() {
    const panel = document.createElement('div');
    panel.className = 'custom-group-panel';
    panel.innerHTML = `
      <div class="panel-backdrop"></div>
      <div class="panel-content">
        <div class="panel-header">
          <h3>自定义分组管理</h3>
          <button class="btn btn-icon close-panel">✕</button>
        </div>
        
        <div class="panel-body">
          <div class="group-tabs">
            <button class="group-tab active" data-tab="groups">我的分组</button>
            <button class="group-tab" data-tab="create">创建分组</button>
            <button class="group-tab" data-tab="manage">管理分组</button>
          </div>
          
          <div class="tab-content active" id="groups-content">
            <div class="groups-section">
              <div class="groups-list" id="customGroupsList">
                ${this.renderCustomGroupsList()}
              </div>
              <div class="groups-actions">
                <button class="btn btn-primary" id="createNewGroup">
                  <span class="icon">➕</span> 新建分组
                </button>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="create-content">
            <div class="create-section">
              <form id="createGroupForm">
                <div class="form-group">
                  <label for="groupName">分组名称</label>
                  <input type="text" id="groupName" class="form-control" placeholder="输入分组名称" required>
                </div>
                
                <div class="form-group">
                  <label for="groupDescription">分组描述</label>
                  <textarea id="groupDescription" class="form-control" rows="3" placeholder="输入分组描述（可选）"></textarea>
                </div>
                
                <div class="form-group">
                  <label>选择图标</label>
                  <div class="icon-selection">
                    ${this.groupIcons.map(icon => `
                      <button type="button" class="icon-option" data-icon="${icon}">${icon}</button>
                    `).join('')}
                  </div>
                </div>
                
                <div class="form-group">
                  <label>选择颜色</label>
                  <div class="color-selection">
                    ${this.groupColors.map(color => `
                      <button type="button" class="color-option" data-color="${color}" style="background-color: ${color}"></button>
                    `).join('')}
                  </div>
                </div>
                
                <div class="form-group">
                  <label>分组规则</label>
                  <div class="rule-selection">
                    <label class="checkbox-label">
                      <input type="checkbox" id="autoGroupByDomain">
                      <span>按域名自动分组</span>
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="autoGroupByTitle">
                      <span>按标题关键词自动分组</span>
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="autoGroupByTag">
                      <span>按标签自动分组</span>
                    </label>
                  </div>
                </div>
                
                <div class="form-group" id="ruleDetailsSection" style="display: none;">
                  <label for="ruleDetails">规则详情</label>
                  <input type="text" id="ruleDetails" class="form-control" placeholder="输入规则详情">
                  <small class="form-help">例如: *.github.com, 关键词: react, 标签: development</small>
                </div>
              </form>
            </div>
          </div>
          
          <div class="tab-content" id="manage-content">
            <div class="manage-section">
              <div class="manage-groups-list" id="manageGroupsList">
                ${this.renderManageGroupsList()}
              </div>
            </div>
          </div>
        </div>
        
        <div class="panel-footer">
          <button class="btn btn-secondary" id="cancelGroup">取消</button>
          <button class="btn btn-primary" id="saveGroup">保存</button>
        </div>
      </div>
    `;

    document.body.appendChild(panel);
    this.bindPanelEvents(panel);
  }

  /**
   * 渲染自定义分组列表
   */
  renderCustomGroupsList() {
    if (this.customGroups.size === 0) {
      return `
        <div class="empty-groups">
          <div class="empty-icon">📁</div>
          <div class="empty-title">还没有自定义分组</div>
          <div class="empty-subtitle">创建分组来更好地组织你的标签页</div>
        </div>
      `;
    }

    return Array.from(this.customGroups.values()).map(group => `
      <div class="group-item" data-group-id="${group.id}">
        <div class="group-icon" style="background-color: ${group.color}">${group.icon}</div>
        <div class="group-info">
          <div class="group-name">${group.name}</div>
          <div class="group-description">${group.description || ''}</div>
          <div class="group-stats">
            <span class="tab-count">${group.tabs.length} 个标签页</span>
            <span class="rule-type">${this.getRuleTypeText(group.rules)}</span>
          </div>
        </div>
        <div class="group-actions">
          <button class="btn btn-small" data-action="view-group" data-group-id="${group.id}">
            <span class="icon">👁️</span>
          </button>
          <button class="btn btn-small" data-action="edit-group" data-group-id="${group.id}">
            <span class="icon">✏️</span>
          </button>
          <button class="btn btn-small" data-action="delete-group" data-group-id="${group.id}">
            <span class="icon">🗑️</span>
          </button>
        </div>
      </div>
    `).join('');
  }

  /**
   * 渲染管理分组列表
   */
  renderManageGroupsList() {
    if (this.customGroups.size === 0) {
      return '<div class="empty-groups">暂无分组需要管理</div>';
    }

    return Array.from(this.customGroups.values()).map(group => `
      <div class="manage-group-item" data-group-id="${group.id}">
        <div class="manage-group-header">
          <div class="group-icon" style="background-color: ${group.color}">${group.icon}</div>
          <div class="group-name">${group.name}</div>
          <div class="group-toggle">
            <label class="toggle-label">
              <input type="checkbox" ${group.enabled ? 'checked' : ''} data-group-id="${group.id}">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
        
        <div class="manage-group-body">
          <div class="group-tabs-list">
            ${group.tabs.map(tab => `
              <div class="group-tab-item" data-tab-id="${tab.id}">
                <img src="${tab.favIconUrl}" class="tab-favicon" onerror="this.style.display='none'">
                <div class="tab-info">
                  <div class="tab-title">${tab.title}</div>
                  <div class="tab-url">${tab.url}</div>
                </div>
                <button class="btn btn-small remove-from-group" data-tab-id="${tab.id}" data-group-id="${group.id}">
                  <span class="icon">✕</span>
                </button>
              </div>
            `).join('')}
          </div>
          
          <div class="group-controls">
            <button class="btn btn-small" data-action="add-tabs" data-group-id="${group.id}">
              <span class="icon">➕</span> 添加标签页
            </button>
            <button class="btn btn-small" data-action="auto-update" data-group-id="${group.id}">
              <span class="icon">🔄</span> 自动更新
            </button>
          </div>
        </div>
      </div>
    `).join('');
  }

  /**
   * 绑定面板事件
   */
  bindPanelEvents(panel) {
    // 关闭面板
    const closePanel = () => {
      panel.remove();
    };

    panel.querySelector('.close-panel').addEventListener('click', closePanel);
    panel.querySelector('.panel-backdrop').addEventListener('click', closePanel);
    panel.querySelector('#cancelGroup').addEventListener('click', closePanel);

    // 标签页切换
    const tabBtns = panel.querySelectorAll('.group-tab');
    const tabContents = panel.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const tab = btn.dataset.tab;
        
        tabBtns.forEach(b => b.classList.remove('active'));
        tabContents.forEach(c => c.classList.remove('active'));
        
        btn.classList.add('active');
        panel.querySelector(`#${tab}-content`).classList.add('active');
      });
    });

    // 创建新分组
    panel.querySelector('#createNewGroup').addEventListener('click', () => {
      // 切换到创建标签页
      tabBtns.forEach(b => b.classList.remove('active'));
      tabContents.forEach(c => c.classList.remove('active'));
      
      panel.querySelector('[data-tab="create"]').classList.add('active');
      panel.querySelector('#create-content').classList.add('active');
    });

    // 图标选择
    panel.querySelectorAll('.icon-option').forEach(btn => {
      btn.addEventListener('click', () => {
        panel.querySelectorAll('.icon-option').forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
      });
    });

    // 颜色选择
    panel.querySelectorAll('.color-option').forEach(btn => {
      btn.addEventListener('click', () => {
        panel.querySelectorAll('.color-option').forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
      });
    });

    // 规则选择
    const ruleCheckboxes = panel.querySelectorAll('[id^="autoGroup"]');
    const ruleDetailsSection = panel.querySelector('#ruleDetailsSection');
    
    ruleCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const hasRuleSelected = Array.from(ruleCheckboxes).some(cb => cb.checked);
        ruleDetailsSection.style.display = hasRuleSelected ? 'block' : 'none';
      });
    });

    // 保存分组
    panel.querySelector('#saveGroup').addEventListener('click', () => {
      this.saveCustomGroup(panel);
    });

    // 分组操作
    panel.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      const groupId = e.target.closest('[data-group-id]')?.dataset.groupId;
      
      if (action && groupId) {
        this.handleGroupAction(action, groupId, panel);
      }
    });

    // 管理分组开关
    panel.querySelectorAll('.manage-group-item input[type="checkbox"]').forEach(toggle => {
      toggle.addEventListener('change', (e) => {
        const groupId = e.target.dataset.groupId;
        this.toggleGroupEnabled(groupId, e.target.checked);
      });
    });

    // 从分组中移除标签页
    panel.querySelectorAll('.remove-from-group').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tabId = e.target.dataset.tabId;
        const groupId = e.target.dataset.groupId;
        this.removeTabFromGroup(tabId, groupId);
        this.refreshManageGroupsList(panel);
      });
    });
  }

  /**
   * 保存自定义分组
   */
  async saveCustomGroup(panel) {
    try {
      const formData = new FormData(panel.querySelector('#createGroupForm'));
      const groupName = panel.querySelector('#groupName').value.trim();
      const groupDescription = panel.querySelector('#groupDescription').value.trim();
      
      if (!groupName) {
        this.app.setStatus('请输入分组名称');
        return;
      }

      const selectedIcon = panel.querySelector('.icon-option.selected')?.dataset.icon || '📁';
      const selectedColor = panel.querySelector('.color-option.selected')?.dataset.color || '#1a73e8';
      
      const rules = {
        byDomain: panel.querySelector('#autoGroupByDomain').checked,
        byTitle: panel.querySelector('#autoGroupByTitle').checked,
        byTag: panel.querySelector('#autoGroupByTag').checked,
        details: panel.querySelector('#ruleDetails').value.trim()
      };

      const groupId = this.generateGroupId();
      const newGroup = {
        id: groupId,
        name: groupName,
        description: groupDescription,
        icon: selectedIcon,
        color: selectedColor,
        rules: rules,
        tabs: [],
        enabled: true,
        createdTime: Date.now(),
        lastModified: Date.now()
      };

      this.customGroups.set(groupId, newGroup);
      await this.saveCustomGroups();
      
      // 应用分组规则
      await this.applyGroupRules(groupId);
      
      this.app.setStatus('分组创建成功');
      panel.remove();
      
      // 刷新界面
      await this.app.refreshData();
      
    } catch (error) {
      console.error('保存自定义分组失败:', error);
      this.app.setStatus('保存分组失败');
    }
  }

  /**
   * 处理分组操作
   */
  async handleGroupAction(action, groupId, panel) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    switch (action) {
      case 'view-group':
        this.viewGroup(groupId);
        break;
      case 'edit-group':
        this.editGroup(groupId, panel);
        break;
      case 'delete-group':
        await this.deleteGroup(groupId);
        this.refreshGroupsList(panel);
        break;
      case 'add-tabs':
        this.showAddTabsDialog(groupId);
        break;
      case 'auto-update':
        await this.applyGroupRules(groupId);
        this.refreshManageGroupsList(panel);
        break;
    }
  }

  /**
   * 应用分组规则
   */
  async applyGroupRules(groupId) {
    const group = this.customGroups.get(groupId);
    if (!group || !group.enabled) return;

    const { rules } = group;
    const allTabs = this.getAllTabs();
    const matchedTabs = [];

    for (const tabInfo of allTabs) {
      const tab = tabInfo.tab;
      const metadata = tabInfo.metadata;
      
      let matches = false;

      // 按域名匹配
      if (rules.byDomain && rules.details) {
        const domain = this.extractDomain(tab.url);
        const domainPattern = rules.details.toLowerCase();
        if (domain.includes(domainPattern) || domainPattern.includes(domain)) {
          matches = true;
        }
      }

      // 按标题关键词匹配
      if (rules.byTitle && rules.details) {
        const titleKeywords = rules.details.toLowerCase();
        if (tab.title.toLowerCase().includes(titleKeywords)) {
          matches = true;
        }
      }

      // 按标签匹配
      if (rules.byTag && rules.details) {
        const tagPattern = rules.details.toLowerCase();
        if (metadata.tags.some(tag => tag.toLowerCase().includes(tagPattern))) {
          matches = true;
        }
      }

      if (matches) {
        matchedTabs.push(tab);
      }
    }

    // 更新分组标签页
    group.tabs = matchedTabs;
    group.lastModified = Date.now();
    
    await this.saveCustomGroups();
  }

  /**
   * 获取所有标签页
   */
  getAllTabs() {
    const allTabs = [];
    this.app.windowGroups.forEach(group => {
      group.tabs.forEach(tab => {
        const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
        allTabs.push({ tab, metadata });
      });
    });
    return allTabs;
  }

  /**
   * 提取域名
   */
  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * 获取规则类型文本
   */
  getRuleTypeText(rules) {
    const types = [];
    if (rules.byDomain) types.push('域名');
    if (rules.byTitle) types.push('标题');
    if (rules.byTag) types.push('标签');
    return types.length > 0 ? types.join(', ') : '手动';
  }

  /**
   * 生成分组ID
   */
  generateGroupId() {
    return 'group_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 查看分组
   */
  viewGroup(groupId) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    // 在主界面中显示分组的标签页
    this.app.setStatus(`正在显示分组: ${group.name}`);
    
    // 过滤显示分组中的标签页
    const filteredGroups = this.app.windowGroups.map(windowGroup => ({
      ...windowGroup,
      tabs: windowGroup.tabs.filter(tab => 
        group.tabs.some(groupTab => groupTab.id === tab.id)
      )
    })).filter(windowGroup => windowGroup.tabs.length > 0);

    // 临时保存原始窗口组
    this.originalWindowGroups = this.app.windowGroups;
    this.app.windowGroups = filteredGroups;
    this.app.renderWindowGroups();
    
    // 显示恢复按钮
    this.showRestoreViewButton(group.name);
  }

  /**
   * 显示恢复视图按钮
   */
  showRestoreViewButton(groupName) {
    const existingBtn = document.querySelector('.restore-view-btn');
    if (existingBtn) existingBtn.remove();

    const toolbar = document.querySelector('.toolbar');
    const restoreBtn = document.createElement('button');
    restoreBtn.className = 'btn btn-secondary restore-view-btn';
    restoreBtn.innerHTML = `<span class="icon">🔙</span> 退出分组视图: ${groupName}`;
    restoreBtn.addEventListener('click', () => {
      this.restoreNormalView();
    });

    toolbar.appendChild(restoreBtn);
  }

  /**
   * 恢复正常视图
   */
  restoreNormalView() {
    if (this.originalWindowGroups) {
      this.app.windowGroups = this.originalWindowGroups;
      this.app.renderWindowGroups();
      delete this.originalWindowGroups;
    }

    const restoreBtn = document.querySelector('.restore-view-btn');
    if (restoreBtn) restoreBtn.remove();

    this.app.setStatus('已恢复正常视图');
  }

  /**
   * 编辑分组
   */
  editGroup(groupId, panel) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    // 切换到创建标签页并填充现有数据
    const tabBtns = panel.querySelectorAll('.group-tab');
    const tabContents = panel.querySelectorAll('.tab-content');
    
    tabBtns.forEach(b => b.classList.remove('active'));
    tabContents.forEach(c => c.classList.remove('active'));
    
    panel.querySelector('[data-tab="create"]').classList.add('active');
    panel.querySelector('#create-content').classList.add('active');

    // 填充表单数据
    panel.querySelector('#groupName').value = group.name;
    panel.querySelector('#groupDescription').value = group.description || '';
    
    // 选择图标和颜色
    panel.querySelectorAll('.icon-option').forEach(btn => {
      btn.classList.toggle('selected', btn.dataset.icon === group.icon);
    });
    
    panel.querySelectorAll('.color-option').forEach(btn => {
      btn.classList.toggle('selected', btn.dataset.color === group.color);
    });
    
    // 设置规则
    panel.querySelector('#autoGroupByDomain').checked = group.rules.byDomain;
    panel.querySelector('#autoGroupByTitle').checked = group.rules.byTitle;
    panel.querySelector('#autoGroupByTag').checked = group.rules.byTag;
    panel.querySelector('#ruleDetails').value = group.rules.details || '';
    
    // 显示规则详情
    const hasRuleSelected = group.rules.byDomain || group.rules.byTitle || group.rules.byTag;
    panel.querySelector('#ruleDetailsSection').style.display = hasRuleSelected ? 'block' : 'none';
    
    // 更新保存按钮为编辑模式
    const saveBtn = panel.querySelector('#saveGroup');
    saveBtn.textContent = '更新分组';
    saveBtn.dataset.editingGroupId = groupId;
  }

  /**
   * 删除分组
   */
  async deleteGroup(groupId) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    const confirmed = confirm(`确定要删除分组 "${group.name}" 吗？`);
    if (!confirmed) return;

    this.customGroups.delete(groupId);
    await this.saveCustomGroups();
    
    this.app.setStatus('分组已删除');
  }

  /**
   * 切换分组启用状态
   */
  async toggleGroupEnabled(groupId, enabled) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    group.enabled = enabled;
    group.lastModified = Date.now();
    
    await this.saveCustomGroups();
    
    if (enabled) {
      await this.applyGroupRules(groupId);
    }
    
    this.app.setStatus(`分组 "${group.name}" 已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 从分组中移除标签页
   */
  removeTabFromGroup(tabId, groupId) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    group.tabs = group.tabs.filter(tab => tab.id !== parseInt(tabId));
    group.lastModified = Date.now();
    
    this.saveCustomGroups();
  }

  /**
   * 刷新分组列表
   */
  refreshGroupsList(panel) {
    const groupsList = panel.querySelector('#customGroupsList');
    if (groupsList) {
      groupsList.innerHTML = this.renderCustomGroupsList();
    }
  }

  /**
   * 刷新管理分组列表
   */
  refreshManageGroupsList(panel) {
    const manageList = panel.querySelector('#manageGroupsList');
    if (manageList) {
      manageList.innerHTML = this.renderManageGroupsList();
    }
  }

  /**
   * 显示添加标签页对话框
   */
  showAddTabsDialog(groupId) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    const dialog = document.createElement('div');
    dialog.className = 'add-tabs-dialog';
    dialog.innerHTML = `
      <div class="dialog-backdrop"></div>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>添加标签页到分组: ${group.name}</h3>
          <button class="close-dialog">✕</button>
        </div>
        <div class="dialog-body">
          <div class="available-tabs">
            ${this.renderAvailableTabs(groupId)}
          </div>
        </div>
        <div class="dialog-footer">
          <button class="btn btn-secondary cancel-add">取消</button>
          <button class="btn btn-primary confirm-add">添加选中的标签页</button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);
    this.bindAddTabsEvents(dialog, groupId);
  }

  /**
   * 渲染可用标签页
   */
  renderAvailableTabs(groupId) {
    const group = this.customGroups.get(groupId);
    const allTabs = this.getAllTabs();
    const groupTabIds = new Set(group.tabs.map(t => t.id));
    
    const availableTabs = allTabs.filter(tabInfo => !groupTabIds.has(tabInfo.tab.id));
    
    if (availableTabs.length === 0) {
      return '<div class="no-tabs">没有可添加的标签页</div>';
    }

    return availableTabs.map(tabInfo => {
      const tab = tabInfo.tab;
      return `
        <div class="available-tab-item">
          <input type="checkbox" class="tab-checkbox" value="${tab.id}">
          <img src="${tab.favIconUrl}" class="tab-favicon" onerror="this.style.display='none'">
          <div class="tab-info">
            <div class="tab-title">${tab.title}</div>
            <div class="tab-url">${tab.url}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 绑定添加标签页事件
   */
  bindAddTabsEvents(dialog, groupId) {
    const closeDialog = () => dialog.remove();
    
    dialog.querySelector('.close-dialog').addEventListener('click', closeDialog);
    dialog.querySelector('.cancel-add').addEventListener('click', closeDialog);
    dialog.querySelector('.dialog-backdrop').addEventListener('click', closeDialog);
    
    dialog.querySelector('.confirm-add').addEventListener('click', () => {
      const selectedTabs = Array.from(dialog.querySelectorAll('.tab-checkbox:checked'))
        .map(cb => parseInt(cb.value));
      
      this.addTabsToGroup(groupId, selectedTabs);
      closeDialog();
    });
  }

  /**
   * 添加标签页到分组
   */
  async addTabsToGroup(groupId, tabIds) {
    const group = this.customGroups.get(groupId);
    if (!group) return;

    const allTabs = this.getAllTabs();
    const tabsToAdd = allTabs.filter(tabInfo => tabIds.includes(tabInfo.tab.id))
      .map(tabInfo => tabInfo.tab);

    group.tabs.push(...tabsToAdd);
    group.lastModified = Date.now();
    
    await this.saveCustomGroups();
    
    this.app.setStatus(`已添加 ${tabsToAdd.length} 个标签页到分组 "${group.name}"`);
  }

  /**
   * 加载自定义分组
   */
  async loadCustomGroups() {
    try {
      const data = await chrome.storage.local.get(['customGroups']);
      if (data.customGroups) {
        this.customGroups = new Map(Object.entries(data.customGroups));
      }
    } catch (error) {
      console.error('加载自定义分组失败:', error);
    }
  }

  /**
   * 保存自定义分组
   */
  async saveCustomGroups() {
    try {
      const groupsData = {};
      for (const [id, group] of this.customGroups) {
        groupsData[id] = group;
      }
      await chrome.storage.local.set({ customGroups: groupsData });
    } catch (error) {
      console.error('保存自定义分组失败:', error);
    }
  }

  /**
   * 绑定自定义分组事件
   */
  bindCustomGroupEvents() {
    // 可以在这里添加更多全局事件监听
  }

  /**
   * 获取分组统计信息
   */
  getGroupStats() {
    const stats = {
      totalGroups: this.customGroups.size,
      enabledGroups: 0,
      totalTabs: 0,
      autoGroups: 0,
      manualGroups: 0
    };

    for (const group of this.customGroups.values()) {
      if (group.enabled) stats.enabledGroups++;
      stats.totalTabs += group.tabs.length;
      
      if (group.rules.byDomain || group.rules.byTitle || group.rules.byTag) {
        stats.autoGroups++;
      } else {
        stats.manualGroups++;
      }
    }

    return stats;
  }

  /**
   * 清理资源
   */
  cleanup() {
    const customGroupBtn = document.querySelector('.custom-group-btn');
    if (customGroupBtn) {
      customGroupBtn.remove();
    }
    
    const panel = document.querySelector('.custom-group-panel');
    if (panel) {
      panel.remove();
    }
    
    const dialog = document.querySelector('.add-tabs-dialog');
    if (dialog) {
      dialog.remove();
    }
    
    const restoreBtn = document.querySelector('.restore-view-btn');
    if (restoreBtn) {
      restoreBtn.remove();
    }
  }
}

export default CustomGroupManager;