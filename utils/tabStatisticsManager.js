/**
 * 标签页统计分析管理器
 * 提供详细的标签页使用统计、分析报告和可视化数据
 */
class TabStatisticsManager {
  constructor(tabManagerApp) {
    this.app = tabManagerApp;
    this.statisticsData = {
      daily: {},
      weekly: {},
      monthly: {},
      domains: {},
      categories: {},
      tags: {},
      usage: {},
      performance: {}
    };
    this.initializeStatistics();
  }

  /**
   * 初始化统计功能
   */
  initializeStatistics() {
    this.loadStatisticsData();
    this.createStatisticsUI();
    this.bindStatisticsEvents();
    this.startDataCollection();
  }

  /**
   * 创建统计UI
   */
  createStatisticsUI() {
    // 检查是否已存在统计按钮
    const existingBtn = document.querySelector('.statistics-btn');
    if (existingBtn) return;

    // 在工具栏添加统计按钮
    const toolbarRight = document.querySelector('.toolbar-right');
    if (!toolbarRight) return;

    const statisticsBtn = document.createElement('button');
    statisticsBtn.className = 'btn btn-icon statistics-btn';
    statisticsBtn.innerHTML = '<span class="icon">📊</span>';
    statisticsBtn.title = '统计分析';
    statisticsBtn.addEventListener('click', () => this.showStatisticsPanel());

    toolbarRight.appendChild(statisticsBtn);
  }

  /**
   * 显示统计面板
   */
  showStatisticsPanel() {
    const panel = document.createElement('div');
    panel.className = 'statistics-panel';
    panel.innerHTML = `
      <div class="panel-backdrop"></div>
      <div class="panel-content">
        <div class="panel-header">
          <h3>标签页统计分析</h3>
          <button class="btn btn-icon close-panel">✕</button>
        </div>
        
        <div class="panel-body">
          <div class="statistics-tabs">
            <button class="stats-tab active" data-tab="overview">概览</button>
            <button class="stats-tab" data-tab="usage">使用情况</button>
            <button class="stats-tab" data-tab="domains">域名分析</button>
            <button class="stats-tab" data-tab="categories">分类统计</button>
            <button class="stats-tab" data-tab="performance">性能分析</button>
            <button class="stats-tab" data-tab="trends">趋势分析</button>
          </div>
          
          <div class="tab-content active" id="overview-content">
            <div class="statistics-overview">
              <h4>总体概览</h4>
              <div class="overview-grid">
                ${this.renderOverviewStats()}
              </div>
              
              <div class="quick-insights">
                <h5>快速洞察</h5>
                <div class="insights-list">
                  ${this.renderQuickInsights()}
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="usage-content">
            <div class="usage-statistics">
              <h4>使用情况统计</h4>
              <div class="usage-charts">
                <div class="chart-container">
                  <h5>每日标签页数量</h5>
                  <div class="chart-area" id="dailyTabsChart">
                    ${this.renderDailyUsageChart()}
                  </div>
                </div>
                <div class="chart-container">
                  <h5>标签页生命周期</h5>
                  <div class="chart-area" id="tabLifecycleChart">
                    ${this.renderTabLifecycleChart()}
                  </div>
                </div>
              </div>
              
              <div class="usage-details">
                <h5>详细使用数据</h5>
                <div class="usage-table">
                  ${this.renderUsageTable()}
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="domains-content">
            <div class="domain-statistics">
              <h4>域名访问分析</h4>
              <div class="domain-summary">
                <div class="summary-grid">
                  ${this.renderDomainSummary()}
                </div>
              </div>
              
              <div class="domain-chart">
                <h5>热门域名分布</h5>
                <div class="chart-area" id="domainChart">
                  ${this.renderDomainChart()}
                </div>
              </div>
              
              <div class="domain-list">
                <h5>域名详细列表</h5>
                <div class="domain-table">
                  ${this.renderDomainTable()}
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="categories-content">
            <div class="category-statistics">
              <h4>分类统计分析</h4>
              <div class="category-overview">
                <div class="overview-grid">
                  ${this.renderCategoryOverview()}
                </div>
              </div>
              
              <div class="category-chart">
                <h5>分类分布图</h5>
                <div class="chart-area" id="categoryChart">
                  ${this.renderCategoryChart()}
                </div>
              </div>
              
              <div class="tags-analysis">
                <h5>标签使用分析</h5>
                <div class="tags-cloud">
                  ${this.renderTagsCloud()}
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="performance-content">
            <div class="performance-statistics">
              <h4>性能分析</h4>
              <div class="performance-overview">
                <div class="performance-grid">
                  ${this.renderPerformanceOverview()}
                </div>
              </div>
              
              <div class="memory-analysis">
                <h5>内存使用分析</h5>
                <div class="memory-chart">
                  ${this.renderMemoryChart()}
                </div>
              </div>
              
              <div class="load-time-analysis">
                <h5>加载时间分析</h5>
                <div class="load-time-chart">
                  ${this.renderLoadTimeChart()}
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-content" id="trends-content">
            <div class="trends-statistics">
              <h4>趋势分析</h4>
              <div class="time-range-selector">
                <label>时间范围：</label>
                <select class="time-range-select">
                  <option value="7">过去7天</option>
                  <option value="30">过去30天</option>
                  <option value="90">过去90天</option>
                  <option value="365">过去一年</option>
                </select>
              </div>
              
              <div class="trends-charts">
                <div class="chart-container">
                  <h5>标签页数量趋势</h5>
                  <div class="chart-area" id="tabTrendsChart">
                    ${this.renderTabTrendsChart()}
                  </div>
                </div>
                <div class="chart-container">
                  <h5>使用模式趋势</h5>
                  <div class="chart-area" id="patternTrendsChart">
                    ${this.renderPatternTrendsChart()}
                  </div>
                </div>
              </div>
              
              <div class="predictions">
                <h5>预测分析</h5>
                <div class="predictions-content">
                  ${this.renderPredictions()}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="panel-footer">
          <button class="btn btn-secondary" id="exportStats">导出统计数据</button>
          <button class="btn btn-secondary" id="resetStats">重置统计</button>
          <button class="btn btn-secondary" id="closeStats">关闭</button>
        </div>
      </div>
    `;

    document.body.appendChild(panel);
    this.bindPanelEvents(panel);
  }

  /**
   * 渲染概览统计
   */
  renderOverviewStats() {
    const stats = this.calculateOverviewStats();
    return `
      <div class="stat-card">
        <div class="stat-icon">📄</div>
        <div class="stat-info">
          <div class="stat-value">${stats.totalTabs}</div>
          <div class="stat-label">总标签页数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🪟</div>
        <div class="stat-info">
          <div class="stat-value">${stats.totalWindows}</div>
          <div class="stat-label">总窗口数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🌐</div>
        <div class="stat-info">
          <div class="stat-value">${stats.uniqueDomains}</div>
          <div class="stat-label">唯一域名数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-info">
          <div class="stat-value">${stats.averageTabsPerWindow}</div>
          <div class="stat-label">平均每窗口标签页数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">💾</div>
        <div class="stat-info">
          <div class="stat-value">${stats.estimatedMemoryUsage}MB</div>
          <div class="stat-label">估计内存使用</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⏱️</div>
        <div class="stat-info">
          <div class="stat-value">${stats.averageLoadTime}ms</div>
          <div class="stat-label">平均加载时间</div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染快速洞察
   */
  renderQuickInsights() {
    const insights = this.generateQuickInsights();
    return insights.map(insight => `
      <div class="insight-item ${insight.type}">
        <div class="insight-icon">${insight.icon}</div>
        <div class="insight-text">${insight.text}</div>
      </div>
    `).join('');
  }

  /**
   * 渲染每日使用图表
   */
  renderDailyUsageChart() {
    const data = this.getDailyUsageData();
    return `
      <div class="simple-chart">
        <div class="chart-bars">
          ${data.map(day => `
            <div class="chart-bar" style="height: ${day.percentage}%">
              <div class="bar-value">${day.count}</div>
              <div class="bar-label">${day.date}</div>
            </div>
          `).join('')}
        </div>
        <div class="chart-legend">
          <span class="legend-item">
            <span class="legend-color" style="background-color: #1a73e8;"></span>
            标签页数量
          </span>
        </div>
      </div>
    `;
  }

  /**
   * 渲染标签页生命周期图表
   */
  renderTabLifecycleChart() {
    const lifecycleData = this.getTabLifecycleData();
    return `
      <div class="lifecycle-chart">
        <div class="lifecycle-stats">
          <div class="lifecycle-stat">
            <div class="stat-value">${lifecycleData.shortLived}</div>
            <div class="stat-label">短期标签页 (<1小时)</div>
          </div>
          <div class="lifecycle-stat">
            <div class="stat-value">${lifecycleData.mediumLived}</div>
            <div class="stat-label">中期标签页 (1-24小时)</div>
          </div>
          <div class="lifecycle-stat">
            <div class="stat-value">${lifecycleData.longLived}</div>
            <div class="stat-label">长期标签页 (>24小时)</div>
          </div>
        </div>
        <div class="lifecycle-pie">
          <div class="pie-chart">
            <div class="pie-segment short" style="--percentage: ${lifecycleData.shortPercentage}%"></div>
            <div class="pie-segment medium" style="--percentage: ${lifecycleData.mediumPercentage}%"></div>
            <div class="pie-segment long" style="--percentage: ${lifecycleData.longPercentage}%"></div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染使用情况表格
   */
  renderUsageTable() {
    const usageData = this.getUsageTableData();
    return `
      <table class="usage-table">
        <thead>
          <tr>
            <th>时间段</th>
            <th>标签页数</th>
            <th>活跃标签页</th>
            <th>关闭标签页</th>
            <th>平均生命周期</th>
          </tr>
        </thead>
        <tbody>
          ${usageData.map(row => `
            <tr>
              <td>${row.timeRange}</td>
              <td>${row.totalTabs}</td>
              <td>${row.activeTabs}</td>
              <td>${row.closedTabs}</td>
              <td>${row.avgLifetime}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;
  }

  /**
   * 渲染域名摘要
   */
  renderDomainSummary() {
    const summary = this.getDomainSummary();
    return `
      <div class="summary-card">
        <div class="summary-value">${summary.totalDomains}</div>
        <div class="summary-label">总域名数</div>
      </div>
      <div class="summary-card">
        <div class="summary-value">${summary.topDomain}</div>
        <div class="summary-label">最常访问域名</div>
      </div>
      <div class="summary-card">
        <div class="summary-value">${summary.avgTabsPerDomain}</div>
        <div class="summary-label">平均每域名标签页数</div>
      </div>
      <div class="summary-card">
        <div class="summary-value">${summary.uniqueDomainsToday}</div>
        <div class="summary-label">今日新域名</div>
      </div>
    `;
  }

  /**
   * 渲染域名图表
   */
  renderDomainChart() {
    const domainData = this.getDomainChartData();
    return `
      <div class="domain-chart">
        <div class="chart-bars horizontal">
          ${domainData.map(domain => `
            <div class="chart-bar-horizontal">
              <div class="bar-label">${domain.domain}</div>
              <div class="bar-container">
                <div class="bar-fill" style="width: ${domain.percentage}%"></div>
                <div class="bar-value">${domain.count}</div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  /**
   * 渲染域名表格
   */
  renderDomainTable() {
    const domainData = this.getDomainTableData();
    return `
      <table class="domain-table">
        <thead>
          <tr>
            <th>域名</th>
            <th>标签页数</th>
            <th>访问频率</th>
            <th>平均停留时间</th>
            <th>最后访问</th>
          </tr>
        </thead>
        <tbody>
          ${domainData.map(domain => `
            <tr>
              <td class="domain-name">
                <img src="https://www.google.com/s2/favicons?domain=${domain.name}" 
                     class="domain-favicon" alt="" width="16" height="16">
                ${domain.name}
              </td>
              <td>${domain.tabCount}</td>
              <td>${domain.frequency}</td>
              <td>${domain.avgStayTime}</td>
              <td>${domain.lastVisit}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;
  }

  /**
   * 渲染分类概览
   */
  renderCategoryOverview() {
    const categoryData = this.getCategoryOverviewData();
    return `
      <div class="category-card">
        <div class="category-value">${categoryData.totalCategories}</div>
        <div class="category-label">总分类数</div>
      </div>
      <div class="category-card">
        <div class="category-value">${categoryData.mostUsedCategory}</div>
        <div class="category-label">最常用分类</div>
      </div>
      <div class="category-card">
        <div class="category-value">${categoryData.uncategorizedTabs}</div>
        <div class="category-label">未分类标签页</div>
      </div>
      <div class="category-card">
        <div class="category-value">${categoryData.avgTabsPerCategory}</div>
        <div class="category-label">平均每分类标签页数</div>
      </div>
    `;
  }

  /**
   * 渲染分类图表
   */
  renderCategoryChart() {
    const categoryData = this.getCategoryChartData();
    return `
      <div class="category-pie-chart">
        <div class="pie-chart-container">
          <div class="pie-chart large">
            ${categoryData.map((category, index) => `
              <div class="pie-segment cat-${index}" 
                   style="--percentage: ${category.percentage}%; --color: ${category.color}">
              </div>
            `).join('')}
          </div>
        </div>
        <div class="pie-legend">
          ${categoryData.map((category, index) => `
            <div class="legend-item">
              <div class="legend-color" style="background-color: ${category.color}"></div>
              <span class="legend-label">${category.name}</span>
              <span class="legend-value">${category.count}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  /**
   * 渲染标签云
   */
  renderTagsCloud() {
    const tagsData = this.getTagsCloudData();
    return `
      <div class="tags-cloud">
        ${tagsData.map(tag => `
          <span class="tag-item" style="font-size: ${tag.size}px; opacity: ${tag.opacity}">
            ${tag.name}
          </span>
        `).join('')}
      </div>
    `;
  }

  /**
   * 渲染性能概览
   */
  renderPerformanceOverview() {
    const perfData = this.getPerformanceOverviewData();
    return `
      <div class="performance-card">
        <div class="perf-icon">⚡</div>
        <div class="perf-info">
          <div class="perf-value">${perfData.avgLoadTime}ms</div>
          <div class="perf-label">平均加载时间</div>
        </div>
      </div>
      <div class="performance-card">
        <div class="perf-icon">💾</div>
        <div class="perf-info">
          <div class="perf-value">${perfData.memoryUsage}MB</div>
          <div class="perf-label">内存使用</div>
        </div>
      </div>
      <div class="performance-card">
        <div class="perf-icon">🔄</div>
        <div class="perf-info">
          <div class="perf-value">${perfData.tabSwitches}</div>
          <div class="perf-label">标签页切换次数</div>
        </div>
      </div>
      <div class="performance-card">
        <div class="perf-icon">📱</div>
        <div class="perf-info">
          <div class="perf-value">${perfData.responsiveScore}</div>
          <div class="perf-label">响应性评分</div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染内存图表
   */
  renderMemoryChart() {
    const memoryData = this.getMemoryChartData();
    return `
      <div class="memory-chart">
        <div class="chart-area">
          <div class="line-chart">
            <svg width="100%" height="200" viewBox="0 0 400 200">
              <polyline points="${memoryData.points}" 
                       stroke="#1a73e8" 
                       stroke-width="2" 
                       fill="none"/>
              <polygon points="${memoryData.points} 400,200 0,200" 
                      fill="rgba(26, 115, 232, 0.1)"/>
            </svg>
          </div>
          <div class="chart-labels">
            ${memoryData.labels.map(label => `
              <div class="chart-label">${label}</div>
            `).join('')}
          </div>
        </div>
        <div class="memory-stats">
          <div class="memory-stat">
            <div class="stat-label">当前使用</div>
            <div class="stat-value">${memoryData.current}MB</div>
          </div>
          <div class="memory-stat">
            <div class="stat-label">峰值使用</div>
            <div class="stat-value">${memoryData.peak}MB</div>
          </div>
          <div class="memory-stat">
            <div class="stat-label">平均使用</div>
            <div class="stat-value">${memoryData.average}MB</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染加载时间图表
   */
  renderLoadTimeChart() {
    const loadTimeData = this.getLoadTimeChartData();
    return `
      <div class="load-time-chart">
        <div class="chart-histogram">
          ${loadTimeData.map(bucket => `
            <div class="histogram-bar" style="height: ${bucket.height}%">
              <div class="bar-count">${bucket.count}</div>
              <div class="bar-range">${bucket.range}</div>
            </div>
          `).join('')}
        </div>
        <div class="load-time-summary">
          <div class="summary-item">
            <div class="summary-label">平均加载时间</div>
            <div class="summary-value">${loadTimeData.average}ms</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">最快加载时间</div>
            <div class="summary-value">${loadTimeData.fastest}ms</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">最慢加载时间</div>
            <div class="summary-value">${loadTimeData.slowest}ms</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染趋势图表
   */
  renderTabTrendsChart() {
    const trendsData = this.getTabTrendsData();
    return `
      <div class="trends-chart">
        <div class="chart-container">
          <svg width="100%" height="300" viewBox="0 0 600 300">
            <defs>
              <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#1a73e8;stop-opacity:0.5" />
                <stop offset="100%" style="stop-color:#1a73e8;stop-opacity:0" />
              </linearGradient>
            </defs>
            <polyline points="${trendsData.points}" 
                     stroke="#1a73e8" 
                     stroke-width="3" 
                     fill="none"/>
            <polygon points="${trendsData.points} 600,300 0,300" 
                    fill="url(#trendGradient)"/>
          </svg>
        </div>
        <div class="trends-info">
          <div class="trend-stat">
            <div class="trend-change ${trendsData.change >= 0 ? 'positive' : 'negative'}">
              ${trendsData.change >= 0 ? '↗' : '↘'} ${Math.abs(trendsData.change)}%
            </div>
            <div class="trend-label">与上周相比</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染模式趋势图表
   */
  renderPatternTrendsChart() {
    const patternData = this.getPatternTrendsData();
    return `
      <div class="pattern-trends-chart">
        <div class="patterns-grid">
          ${patternData.map(pattern => `
            <div class="pattern-item">
              <div class="pattern-name">${pattern.name}</div>
              <div class="pattern-trend ${pattern.trend}">
                <div class="trend-arrow">${pattern.trend === 'up' ? '↗' : pattern.trend === 'down' ? '↘' : '→'}</div>
                <div class="trend-value">${pattern.value}%</div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  /**
   * 渲染预测分析
   */
  renderPredictions() {
    const predictions = this.generatePredictions();
    return `
      <div class="predictions-grid">
        ${predictions.map(prediction => `
          <div class="prediction-card">
            <div class="prediction-icon">${prediction.icon}</div>
            <div class="prediction-content">
              <div class="prediction-title">${prediction.title}</div>
              <div class="prediction-description">${prediction.description}</div>
              <div class="prediction-confidence">
                可信度: ${prediction.confidence}%
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * 绑定面板事件
   */
  bindPanelEvents(panel) {
    // 关闭面板
    const closePanel = () => {
      panel.remove();
    };

    panel.querySelector('.close-panel').addEventListener('click', closePanel);
    panel.querySelector('.panel-backdrop').addEventListener('click', closePanel);
    panel.querySelector('#closeStats').addEventListener('click', closePanel);

    // 标签页切换
    const tabBtns = panel.querySelectorAll('.stats-tab');
    const tabContents = panel.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const tab = btn.dataset.tab;
        
        tabBtns.forEach(b => b.classList.remove('active'));
        tabContents.forEach(c => c.classList.remove('active'));
        
        btn.classList.add('active');
        panel.querySelector(`#${tab}-content`).classList.add('active');
      });
    });

    // 时间范围选择
    const timeRangeSelect = panel.querySelector('.time-range-select');
    if (timeRangeSelect) {
      timeRangeSelect.addEventListener('change', (e) => {
        this.updateTrendsData(parseInt(e.target.value));
      });
    }

    // 导出统计数据
    panel.querySelector('#exportStats').addEventListener('click', () => {
      this.exportStatisticsData();
    });

    // 重置统计
    panel.querySelector('#resetStats').addEventListener('click', () => {
      this.resetStatistics();
    });
  }

  /**
   * 计算概览统计
   */
  calculateOverviewStats() {
    const allTabs = this.getAllTabs();
    const allDomains = new Set();
    let totalMemory = 0;
    let totalLoadTime = 0;
    
    allTabs.forEach(tab => {
      try {
        const domain = new URL(tab.url).hostname;
        allDomains.add(domain);
      } catch (e) {
        // 忽略无效URL
      }
      
      // 估算内存和加载时间
      totalMemory += this.estimateTabMemory(tab);
      totalLoadTime += this.estimateLoadTime(tab);
    });

    return {
      totalTabs: allTabs.length,
      totalWindows: this.app.windowGroups.length,
      uniqueDomains: allDomains.size,
      averageTabsPerWindow: Math.round(allTabs.length / this.app.windowGroups.length || 0),
      estimatedMemoryUsage: Math.round(totalMemory),
      averageLoadTime: Math.round(totalLoadTime / allTabs.length || 0)
    };
  }

  /**
   * 生成快速洞察
   */
  generateQuickInsights() {
    const insights = [];
    const stats = this.calculateOverviewStats();
    
    // 基于统计数据生成洞察
    if (stats.totalTabs > 20) {
      insights.push({
        type: 'warning',
        icon: '⚠️',
        text: `您当前打开了 ${stats.totalTabs} 个标签页，建议关闭不必要的标签页以提高性能。`
      });
    }

    if (stats.uniqueDomains > 10) {
      insights.push({
        type: 'info',
        icon: '🌐',
        text: `您访问了 ${stats.uniqueDomains} 个不同的域名，显示了多样化的浏览习惯。`
      });
    }

    if (stats.estimatedMemoryUsage > 500) {
      insights.push({
        type: 'warning',
        icon: '💾',
        text: `估计内存使用量为 ${stats.estimatedMemoryUsage}MB，建议清理一些标签页。`
      });
    }

    insights.push({
      type: 'tip',
      icon: '💡',
      text: '使用自定义分组功能可以更好地组织您的标签页。'
    });

    return insights;
  }

  /**
   * 获取每日使用数据
   */
  getDailyUsageData() {
    const data = [];
    const maxCount = 50; // 用于计算百分比
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' });
      
      // 模拟数据，实际应该从存储中获取
      const count = Math.floor(Math.random() * 30) + 10;
      
      data.push({
        date: dateStr,
        count: count,
        percentage: (count / maxCount) * 100
      });
    }
    
    return data;
  }

  /**
   * 获取标签页生命周期数据
   */
  getTabLifecycleData() {
    // 模拟数据，实际应该基于真实的标签页使用统计
    const total = 100;
    const shortLived = 45;
    const mediumLived = 35;
    const longLived = 20;
    
    return {
      shortLived,
      mediumLived,
      longLived,
      shortPercentage: (shortLived / total) * 100,
      mediumPercentage: (mediumLived / total) * 100,
      longPercentage: (longLived / total) * 100
    };
  }

  /**
   * 获取使用表格数据
   */
  getUsageTableData() {
    return [
      { timeRange: '今天', totalTabs: 25, activeTabs: 12, closedTabs: 8, avgLifetime: '2.5小时' },
      { timeRange: '昨天', totalTabs: 32, activeTabs: 18, closedTabs: 14, avgLifetime: '3.2小时' },
      { timeRange: '本周', totalTabs: 184, activeTabs: 85, closedTabs: 99, avgLifetime: '4.1小时' },
      { timeRange: '上周', totalTabs: 156, activeTabs: 72, closedTabs: 84, avgLifetime: '3.8小时' },
      { timeRange: '本月', totalTabs: 742, activeTabs: 368, closedTabs: 374, avgLifetime: '5.2小时' }
    ];
  }

  /**
   * 获取域名摘要
   */
  getDomainSummary() {
    const allTabs = this.getAllTabs();
    const domainCounts = new Map();
    
    allTabs.forEach(tab => {
      try {
        const domain = new URL(tab.url).hostname;
        domainCounts.set(domain, (domainCounts.get(domain) || 0) + 1);
      } catch (e) {
        // 忽略无效URL
      }
    });

    const domains = Array.from(domainCounts.entries());
    const topDomain = domains.sort((a, b) => b[1] - a[1])[0];
    
    return {
      totalDomains: domains.length,
      topDomain: topDomain ? topDomain[0] : 'N/A',
      avgTabsPerDomain: Math.round(allTabs.length / domains.length || 0),
      uniqueDomainsToday: Math.floor(domains.length * 0.3) // 模拟数据
    };
  }

  /**
   * 获取域名图表数据
   */
  getDomainChartData() {
    const allTabs = this.getAllTabs();
    const domainCounts = new Map();
    
    allTabs.forEach(tab => {
      try {
        const domain = new URL(tab.url).hostname;
        domainCounts.set(domain, (domainCounts.get(domain) || 0) + 1);
      } catch (e) {
        // 忽略无效URL
      }
    });

    const sortedDomains = Array.from(domainCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10); // 取前10个

    const maxCount = sortedDomains[0] ? sortedDomains[0][1] : 1;
    
    return sortedDomains.map(([domain, count]) => ({
      domain,
      count,
      percentage: (count / maxCount) * 100
    }));
  }

  /**
   * 获取域名表格数据
   */
  getDomainTableData() {
    const domainChart = this.getDomainChartData();
    
    return domainChart.map(domain => ({
      name: domain.domain,
      tabCount: domain.count,
      frequency: Math.floor(Math.random() * 20) + 1, // 模拟数据
      avgStayTime: `${Math.floor(Math.random() * 60) + 5}分钟`,
      lastVisit: this.getRandomRecentTime()
    }));
  }

  /**
   * 获取分类概览数据
   */
  getCategoryOverviewData() {
    const allTabs = this.getAllTabs();
    const categories = new Map();
    let uncategorizedCount = 0;
    
    allTabs.forEach(tab => {
      const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
      if (metadata.category) {
        categories.set(metadata.category, (categories.get(metadata.category) || 0) + 1);
      } else {
        uncategorizedCount++;
      }
    });

    const sortedCategories = Array.from(categories.entries()).sort((a, b) => b[1] - a[1]);
    
    return {
      totalCategories: categories.size,
      mostUsedCategory: sortedCategories[0] ? sortedCategories[0][0] : 'N/A',
      uncategorizedTabs: uncategorizedCount,
      avgTabsPerCategory: Math.round(allTabs.length / categories.size || 0)
    };
  }

  /**
   * 获取分类图表数据
   */
  getCategoryChartData() {
    const allTabs = this.getAllTabs();
    const categories = new Map();
    let uncategorizedCount = 0;
    
    allTabs.forEach(tab => {
      const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
      if (metadata.category) {
        categories.set(metadata.category, (categories.get(metadata.category) || 0) + 1);
      } else {
        uncategorizedCount++;
      }
    });

    const data = Array.from(categories.entries()).map(([name, count]) => ({
      name,
      count,
      percentage: (count / allTabs.length) * 100,
      color: this.getCategoryColor(name)
    }));

    if (uncategorizedCount > 0) {
      data.push({
        name: '未分类',
        count: uncategorizedCount,
        percentage: (uncategorizedCount / allTabs.length) * 100,
        color: '#9aa0a6'
      });
    }

    return data;
  }

  /**
   * 获取标签云数据
   */
  getTagsCloudData() {
    const allTabs = this.getAllTabs();
    const tagCounts = new Map();
    
    allTabs.forEach(tab => {
      const metadata = this.app.tabNotesManager.getTabMetadata(tab.id);
      metadata.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    const sortedTags = Array.from(tagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 30); // 取前30个标签

    const maxCount = sortedTags[0] ? sortedTags[0][1] : 1;
    
    return sortedTags.map(([name, count]) => ({
      name,
      count,
      size: 12 + (count / maxCount) * 20, // 12px 到 32px
      opacity: 0.6 + (count / maxCount) * 0.4 // 0.6 到 1.0
    }));
  }

  /**
   * 获取性能概览数据
   */
  getPerformanceOverviewData() {
    const allTabs = this.getAllTabs();
    
    return {
      avgLoadTime: Math.round(allTabs.reduce((sum, tab) => sum + this.estimateLoadTime(tab), 0) / allTabs.length || 0),
      memoryUsage: Math.round(allTabs.reduce((sum, tab) => sum + this.estimateTabMemory(tab), 0)),
      tabSwitches: Math.floor(Math.random() * 100) + 50, // 模拟数据
      responsiveScore: Math.floor(Math.random() * 20) + 80 // 模拟数据
    };
  }

  /**
   * 获取内存图表数据
   */
  getMemoryChartData() {
    const points = [];
    const labels = [];
    let currentMemory = 300;
    
    for (let i = 0; i < 24; i++) {
      const hour = i;
      const x = (i / 23) * 400;
      const variation = (Math.random() - 0.5) * 50;
      currentMemory = Math.max(200, Math.min(800, currentMemory + variation));
      const y = 200 - ((currentMemory - 200) / 600) * 200;
      
      points.push(`${x},${y}`);
      
      if (i % 4 === 0) {
        labels.push(`${hour}:00`);
      }
    }
    
    return {
      points: points.join(' '),
      labels,
      current: Math.round(currentMemory),
      peak: 785,
      average: 450
    };
  }

  /**
   * 获取加载时间图表数据
   */
  getLoadTimeChartData() {
    const buckets = [
      { range: '0-100ms', count: 15, height: 60 },
      { range: '100-300ms', count: 25, height: 100 },
      { range: '300-500ms', count: 18, height: 72 },
      { range: '500-1s', count: 12, height: 48 },
      { range: '1s-2s', count: 8, height: 32 },
      { range: '>2s', count: 3, height: 12 }
    ];
    
    return {
      ...buckets,
      average: 425,
      fastest: 68,
      slowest: 3200
    };
  }

  /**
   * 获取趋势数据
   */
  getTabTrendsData() {
    const points = [];
    let currentValue = 20;
    
    for (let i = 0; i < 30; i++) {
      const x = (i / 29) * 600;
      const variation = (Math.random() - 0.5) * 5;
      currentValue = Math.max(10, Math.min(50, currentValue + variation));
      const y = 300 - ((currentValue - 10) / 40) * 280;
      
      points.push(`${x},${y}`);
    }
    
    return {
      points: points.join(' '),
      change: Math.floor(Math.random() * 30) - 15 // -15% 到 +15%
    };
  }

  /**
   * 获取模式趋势数据
   */
  getPatternTrendsData() {
    return [
      { name: '工作日使用', value: 15, trend: 'up' },
      { name: '周末使用', value: 8, trend: 'down' },
      { name: '夜间使用', value: 12, trend: 'up' },
      { name: '移动设备', value: 5, trend: 'stable' },
      { name: '多标签页', value: 25, trend: 'up' },
      { name: '长时间会话', value: 18, trend: 'down' }
    ];
  }

  /**
   * 生成预测分析
   */
  generatePredictions() {
    return [
      {
        icon: '📈',
        title: '标签页使用趋势',
        description: '基于历史数据，预测下周标签页使用量将增加12%',
        confidence: 85
      },
      {
        icon: '🎯',
        title: '优化建议',
        description: '建议在周二下午3点清理不活跃的标签页以提高性能',
        confidence: 78
      },
      {
        icon: '🚀',
        title: '性能预测',
        description: '继续当前使用模式，系统响应速度将保持稳定',
        confidence: 92
      }
    ];
  }

  /**
   * 辅助方法
   */
  getAllTabs() {
    const allTabs = [];
    this.app.windowGroups.forEach(group => {
      allTabs.push(...group.tabs);
    });
    return allTabs;
  }

  estimateTabMemory(tab) {
    // 简单的内存估算
    const baseMemory = 50; // MB
    const titleFactor = tab.title.length * 0.1;
    const urlFactor = tab.url.length * 0.05;
    return baseMemory + titleFactor + urlFactor;
  }

  estimateLoadTime(tab) {
    // 简单的加载时间估算
    const baseTime = 200; // ms
    const urlComplexity = tab.url.length * 2;
    const randomVariation = Math.random() * 300;
    return baseTime + urlComplexity + randomVariation;
  }

  getCategoryColor(categoryName) {
    const colors = [
      '#1a73e8', '#34a853', '#fbbc04', '#ea4335', '#9aa0a6',
      '#ff6d01', '#d2e3fc', '#e8f0fe', '#fce8e6', '#f3e8fd'
    ];
    const hash = categoryName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return colors[hash % colors.length];
  }

  getRandomRecentTime() {
    const now = new Date();
    const hoursAgo = Math.floor(Math.random() * 24);
    const timeAgo = new Date(now.getTime() - hoursAgo * 60 * 60 * 1000);
    return timeAgo.toLocaleString('zh-CN');
  }

  /**
   * 开始数据收集
   */
  startDataCollection() {
    // 每分钟收集一次数据
    setInterval(() => {
      this.collectCurrentData();
    }, 60000);
    
    // 立即收集一次数据
    this.collectCurrentData();
  }

  /**
   * 收集当前数据
   */
  collectCurrentData() {
    const now = new Date();
    const dateKey = now.toISOString().split('T')[0];
    const hourKey = now.getHours();
    
    // 收集每日数据
    if (!this.statisticsData.daily[dateKey]) {
      this.statisticsData.daily[dateKey] = {
        tabCount: 0,
        windowCount: 0,
        domains: new Set(),
        categories: new Map(),
        hourlyData: {}
      };
    }
    
    const dailyData = this.statisticsData.daily[dateKey];
    dailyData.tabCount = this.app.getTotalTabCount();
    dailyData.windowCount = this.app.windowGroups.length;
    
    // 收集每小时数据
    if (!dailyData.hourlyData[hourKey]) {
      dailyData.hourlyData[hourKey] = {
        tabCount: 0,
        memoryUsage: 0,
        loadTime: 0
      };
    }
    
    const hourlyData = dailyData.hourlyData[hourKey];
    hourlyData.tabCount = this.app.getTotalTabCount();
    hourlyData.memoryUsage = this.calculateCurrentMemoryUsage();
    hourlyData.loadTime = this.calculateAverageLoadTime();
    
    // 收集域名数据
    this.getAllTabs().forEach(tab => {
      try {
        const domain = new URL(tab.url).hostname;
        dailyData.domains.add(domain);
        
        if (!this.statisticsData.domains[domain]) {
          this.statisticsData.domains[domain] = {
            visits: 0,
            totalTime: 0,
            lastVisit: now
          };
        }
        
        this.statisticsData.domains[domain].visits++;
        this.statisticsData.domains[domain].lastVisit = now;
      } catch (e) {
        // 忽略无效URL
      }
    });
    
    // 保存数据
    this.saveStatisticsData();
  }

  /**
   * 计算当前内存使用
   */
  calculateCurrentMemoryUsage() {
    return this.getAllTabs().reduce((sum, tab) => sum + this.estimateTabMemory(tab), 0);
  }

  /**
   * 计算平均加载时间
   */
  calculateAverageLoadTime() {
    const tabs = this.getAllTabs();
    const totalTime = tabs.reduce((sum, tab) => sum + this.estimateLoadTime(tab), 0);
    return tabs.length > 0 ? totalTime / tabs.length : 0;
  }

  /**
   * 更新趋势数据
   */
  updateTrendsData(days) {
    // 重新渲染趋势图表
    const trendsChart = document.querySelector('#tabTrendsChart');
    const patternChart = document.querySelector('#patternTrendsChart');
    
    if (trendsChart) {
      trendsChart.innerHTML = this.renderTabTrendsChart();
    }
    
    if (patternChart) {
      patternChart.innerHTML = this.renderPatternTrendsChart();
    }
  }

  /**
   * 导出统计数据
   */
  exportStatisticsData() {
    const exportData = {
      exportTime: new Date().toISOString(),
      version: '1.0',
      statistics: this.statisticsData
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tab-statistics-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    this.app.setStatus('统计数据已导出');
  }

  /**
   * 重置统计
   */
  resetStatistics() {
    const confirmed = confirm('确定要重置所有统计数据吗？此操作不可撤销。');
    if (confirmed) {
      this.statisticsData = {
        daily: {},
        weekly: {},
        monthly: {},
        domains: {},
        categories: {},
        tags: {},
        usage: {},
        performance: {}
      };
      
      this.saveStatisticsData();
      this.app.setStatus('统计数据已重置');
      
      // 重新渲染面板
      const existingPanel = document.querySelector('.statistics-panel');
      if (existingPanel) {
        existingPanel.remove();
        this.showStatisticsPanel();
      }
    }
  }

  /**
   * 加载统计数据
   */
  async loadStatisticsData() {
    try {
      const data = await chrome.storage.local.get(['statisticsData']);
      if (data.statisticsData) {
        this.statisticsData = data.statisticsData;
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  }

  /**
   * 保存统计数据
   */
  async saveStatisticsData() {
    try {
      await chrome.storage.local.set({ statisticsData: this.statisticsData });
    } catch (error) {
      console.error('保存统计数据失败:', error);
    }
  }

  /**
   * 绑定统计事件
   */
  bindStatisticsEvents() {
    // 监听标签页变化
    document.addEventListener('tabs-updated', () => {
      this.collectCurrentData();
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    const statisticsBtn = document.querySelector('.statistics-btn');
    if (statisticsBtn) {
      statisticsBtn.remove();
    }
    
    const panel = document.querySelector('.statistics-panel');
    if (panel) {
      panel.remove();
    }
  }
}

export default TabStatisticsManager;