// Background Service Worker for Chrome Tab Manager
// 处理扩展的后台逻辑和API调用

// 导入工具模块
importScripts('../utils/chromeAPI.js');
importScripts('../utils/storageManager.js');

class BackgroundService {
  constructor() {
    this.isInitialized = false;
    this.tabData = new Map();
    this.windowData = new Map();
    
    this.init();
  }

  async init() {
    try {
      console.log('Background service initializing...');
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 初始化数据
      await this.syncTabData();
      
      this.isInitialized = true;
      console.log('Background service initialized successfully');
      
    } catch (error) {
      console.error('Background service initialization failed:', error);
    }
  }

  /**
   * 设置Chrome API事件监听器
   */
  setupEventListeners() {
    // 标签页事件监听
    if (chrome.tabs) {
      chrome.tabs.onCreated.addListener((tab) => {
        console.log('Tab created:', tab.id);
        this.handleTabCreated(tab);
      });

      chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
        console.log('Tab removed:', tabId);
        this.handleTabRemoved(tabId, removeInfo);
      });

      chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        this.handleTabUpdated(tabId, changeInfo, tab);
      });

      chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
        console.log('Tab moved:', tabId);
        this.handleTabMoved(tabId, moveInfo);
      });

      chrome.tabs.onActivated.addListener((activeInfo) => {
        console.log('Tab activated:', activeInfo.tabId);
        this.handleTabActivated(activeInfo);
      });
    }

    // 窗口事件监听
    if (chrome.windows) {
      chrome.windows.onCreated.addListener((window) => {
        console.log('Window created:', window.id);
        this.handleWindowCreated(window);
      });

      chrome.windows.onRemoved.addListener((windowId) => {
        console.log('Window removed:', windowId);
        this.handleWindowRemoved(windowId);
      });

      chrome.windows.onFocusChanged.addListener((windowId) => {
        this.handleWindowFocusChanged(windowId);
      });
    }

    // 扩展消息监听
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放以进行异步响应
    });

    // 安装和启动事件
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });

    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });
  }

  /**
   * 同步标签页数据
   */
  async syncTabData() {
    try {
      const windows = await chrome.windows.getAll({ populate: true });
      
      // 清空现有数据
      this.tabData.clear();
      this.windowData.clear();
      
      // 重新构建数据
      for (const window of windows) {
        this.windowData.set(window.id, {
          id: window.id,
          focused: window.focused,
          incognito: window.incognito,
          type: window.type,
          state: window.state,
          tabCount: window.tabs ? window.tabs.length : 0
        });

        if (window.tabs) {
          for (const tab of window.tabs) {
            this.tabData.set(tab.id, {
              id: tab.id,
              windowId: tab.windowId,
              title: tab.title,
              url: tab.url,
              favIconUrl: tab.favIconUrl,
              active: tab.active,
              pinned: tab.pinned,
              index: tab.index,
              status: tab.status
            });
          }
        }
      }

      console.log(`Synced data: ${this.windowData.size} windows, ${this.tabData.size} tabs`);
      
    } catch (error) {
      console.error('Failed to sync tab data:', error);
    }
  }

  /**
   * 标签页事件处理器
   */
  async handleTabCreated(tab) {
    this.tabData.set(tab.id, {
      id: tab.id,
      windowId: tab.windowId,
      title: tab.title,
      url: tab.url,
      favIconUrl: tab.favIconUrl,
      active: tab.active,
      pinned: tab.pinned,
      index: tab.index,
      status: tab.status
    });

    // 更新窗口标签页计数
    if (this.windowData.has(tab.windowId)) {
      const windowInfo = this.windowData.get(tab.windowId);
      windowInfo.tabCount++;
      this.windowData.set(tab.windowId, windowInfo);
    }

    // 通知popup更新
    this.notifyPopupUpdate('tab-created', { tab });
  }

  async handleTabRemoved(tabId, removeInfo) {
    this.tabData.delete(tabId);

    // 更新窗口标签页计数
    if (this.windowData.has(removeInfo.windowId)) {
      const windowInfo = this.windowData.get(removeInfo.windowId);
      windowInfo.tabCount = Math.max(0, windowInfo.tabCount - 1);
      this.windowData.set(removeInfo.windowId, windowInfo);
    }

    // 通知popup更新
    this.notifyPopupUpdate('tab-removed', { tabId, removeInfo });
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    if (this.tabData.has(tabId)) {
      const existingTab = this.tabData.get(tabId);
      const updatedTab = { ...existingTab, ...changeInfo };
      
      // 更新完整信息
      updatedTab.title = tab.title;
      updatedTab.url = tab.url;
      updatedTab.favIconUrl = tab.favIconUrl;
      updatedTab.status = tab.status;
      
      this.tabData.set(tabId, updatedTab);

      // 如果是重要更新（标题或URL改变），通知popup
      if (changeInfo.title || changeInfo.url || changeInfo.status === 'complete') {
        this.notifyPopupUpdate('tab-updated', { tabId, changeInfo, tab });
      }
    }
  }

  async handleTabMoved(tabId, moveInfo) {
    if (this.tabData.has(tabId)) {
      const tab = this.tabData.get(tabId);
      tab.index = moveInfo.toIndex;
      tab.windowId = moveInfo.windowId;
      this.tabData.set(tabId, tab);
    }

    this.notifyPopupUpdate('tab-moved', { tabId, moveInfo });
  }

  async handleTabActivated(activeInfo) {
    // 更新所有标签页的active状态
    for (const [id, tab] of this.tabData) {
      if (tab.windowId === activeInfo.windowId) {
        tab.active = (id === activeInfo.tabId);
        this.tabData.set(id, tab);
      }
    }

    this.notifyPopupUpdate('tab-activated', activeInfo);
  }

  /**
   * 窗口事件处理器
   */
  async handleWindowCreated(window) {
    this.windowData.set(window.id, {
      id: window.id,
      focused: window.focused,
      incognito: window.incognito,
      type: window.type,
      state: window.state,
      tabCount: 0
    });

    this.notifyPopupUpdate('window-created', { window });
  }

  async handleWindowRemoved(windowId) {
    // 删除窗口数据
    this.windowData.delete(windowId);
    
    // 删除该窗口的所有标签页数据
    for (const [tabId, tab] of this.tabData) {
      if (tab.windowId === windowId) {
        this.tabData.delete(tabId);
      }
    }

    this.notifyPopupUpdate('window-removed', { windowId });
  }

  async handleWindowFocusChanged(windowId) {
    // 更新窗口焦点状态
    for (const [id, window] of this.windowData) {
      window.focused = (id === windowId);
      this.windowData.set(id, window);
    }

    this.notifyPopupUpdate('window-focus-changed', { windowId });
  }

  /**
   * 消息处理器
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      console.log('Received message:', message.type);
      
      switch (message.type) {
        case 'GET_ALL_TABS':
          const allTabs = await chromeAPI.getAllTabsGroupedByWindow();
          sendResponse({ success: true, data: allTabs });
          break;

        case 'OPEN_TAB_NEW_WINDOW':
          await chromeAPI.openTabInNewWindow(message.url);
          sendResponse({ success: true });
          break;

        case 'OPEN_TAB_CURRENT_WINDOW':
          await chromeAPI.openTabInCurrentWindow(message.url, message.windowId);
          sendResponse({ success: true });
          break;

        case 'CLOSE_TAB':
          const closeResult = await chromeAPI.closeTab(message.tabId);
          sendResponse({ success: closeResult });
          break;

        case 'CLOSE_TABS':
          const closeMultipleResult = await chromeAPI.closeTabs(message.tabIds);
          sendResponse({ success: closeMultipleResult });
          break;

        case 'CLOSE_WINDOW':
          const closeWindowResult = await chromeAPI.closeWindow(message.windowId);
          sendResponse({ success: closeWindowResult });
          break;

        case 'ACTIVATE_TAB':
          const activateResult = await chromeAPI.activateTab(message.tabId);
          sendResponse({ success: activateResult });
          break;

        case 'MOVE_TAB':
          const moveResult = await chromeAPI.moveTabToWindow(
            message.tabId, 
            message.targetWindowId, 
            message.index
          );
          sendResponse({ success: moveResult });
          break;

        case 'SEARCH_TABS':
          const searchResults = await chromeAPI.searchTabs(message.query);
          sendResponse({ success: true, data: searchResults });
          break;

        case 'GET_TAB_INFO':
          const tabInfo = await chromeAPI.getTabInfo(message.tabId);
          sendResponse({ success: true, data: tabInfo });
          break;

        case 'SYNC_DATA':
          await this.syncTabData();
          sendResponse({ success: true });
          break;

        // 拖拽排序相关消息处理
        case 'REORDER_TABS':
          const reorderResult = await this.handleReorderTabs(message);
          sendResponse({ success: reorderResult });
          break;

        case 'MOVE_TAB_TO_WINDOW':
          const moveToWindowResult = await this.handleMoveTabToWindow(message);
          sendResponse({ success: moveToWindowResult });
          break;

        case 'MOVE_TAB_TO_NEW_WINDOW':
          const moveToNewWindowResult = await this.handleMoveTabToNewWindow(message);
          sendResponse({ success: moveToNewWindowResult });
          break;

        case 'REORDER_WINDOW_GROUPS':
          const reorderWindowsResult = await this.handleReorderWindowGroups(message);
          sendResponse({ success: reorderWindowsResult });
          break;

        case 'RESTORE_TAB':
          const restoreResult = await this.handleRestoreTab(message);
          sendResponse({ success: restoreResult });
          break;

        case 'RESTORE_WINDOW':
          const restoreWindowResult = await this.handleRestoreWindow(message);
          sendResponse({ success: restoreWindowResult });
          break;

        default:
          console.warn('Unknown message type:', message.type);
          sendResponse({ success: false, error: 'Unknown message type' });
      }
      
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 安装事件处理器
   */
  async handleInstalled(details) {
    console.log('Extension installed:', details.reason);
    
    if (details.reason === 'install') {
      // 首次安装
      await this.setupDefaultSettings();
    } else if (details.reason === 'update') {
      // 更新
      await this.handleUpdate(details.previousVersion);
    }
  }

  async handleStartup() {
    console.log('Extension startup');
    await this.syncTabData();
  }

  /**
   * 设置默认配置
   */
  async setupDefaultSettings() {
    try {
      const defaultSettings = storageManager.getDefaultSettings();
      await storageManager.saveUserSettings(defaultSettings);
      console.log('Default settings saved');
    } catch (error) {
      console.error('Failed to setup default settings:', error);
    }
  }

  /**
   * 处理扩展更新
   */
  async handleUpdate(previousVersion) {
    console.log(`Extension updated from ${previousVersion}`);
    // 可以在这里处理数据迁移等逻辑
  }

  /**
   * 处理标签页重新排序
   */
  async handleReorderTabs(message) {
    try {
      const { tabId, targetTabId, windowId } = message;
      
      // 获取目标标签页的索引
      const targetTab = await chrome.tabs.get(targetTabId);
      const newIndex = targetTab.index;
      
      // 移动标签页到新位置
      await chrome.tabs.move(tabId, { index: newIndex });
      
      console.log(`Tab ${tabId} reordered to index ${newIndex} in window ${windowId}`);
      return true;
    } catch (error) {
      console.error('Failed to reorder tabs:', error);
      return false;
    }
  }

  /**
   * 处理标签页在窗口间移动
   */
  async handleMoveTabToWindow(message) {
    try {
      const { tabId, targetWindowId, insertBeforeTabId } = message;
      
      let moveOptions = { windowId: targetWindowId };
      
      // 如果指定了插入位置
      if (insertBeforeTabId) {
        const targetTab = await chrome.tabs.get(insertBeforeTabId);
        moveOptions.index = targetTab.index;
      } else {
        // 默认插入到末尾
        moveOptions.index = -1;
      }
      
      await chrome.tabs.move(tabId, moveOptions);
      
      console.log(`Tab ${tabId} moved to window ${targetWindowId}`);
      return true;
    } catch (error) {
      console.error('Failed to move tab to window:', error);
      return false;
    }
  }

  /**
   * 处理标签页移动到新窗口
   */
  async handleMoveTabToNewWindow(message) {
    try {
      const { tabId } = message;
      
      // 获取当前标签页信息
      const tab = await chrome.tabs.get(tabId);
      
      // 创建新窗口并移动标签页
      const newWindow = await chrome.windows.create({
        tabId: tabId,
        type: 'normal',
        focused: true
      });
      
      console.log(`Tab ${tabId} moved to new window ${newWindow.id}`);
      return true;
    } catch (error) {
      console.error('Failed to move tab to new window:', error);
      return false;
    }
  }

  /**
   * 处理窗口组重新排序
   */
  async handleReorderWindowGroups(message) {
    try {
      const { sourceWindowId, targetWindowId } = message;
      
      // Chrome API不直接支持窗口重新排序
      // 这里可以通过更新窗口的focused状态来模拟排序效果
      await chrome.windows.update(sourceWindowId, { focused: true });
      
      console.log(`Window group reordered: ${sourceWindowId} -> ${targetWindowId}`);
      return true;
    } catch (error) {
      console.error('Failed to reorder window groups:', error);
      return false;
    }
  }

  /**
   * 处理标签页恢复
   */
  async handleRestoreTab(message) {
    try {
      const { url, windowId, index, active, pinned } = message;
      
      const createOptions = {
        url: url,
        windowId: windowId,
        active: active || false,
        pinned: pinned || false
      };
      
      if (index !== undefined) {
        createOptions.index = index;
      }
      
      const newTab = await chrome.tabs.create(createOptions);
      
      console.log(`Tab restored: ${newTab.id} at ${url}`);
      return true;
    } catch (error) {
      console.error('Failed to restore tab:', error);
      return false;
    }
  }

  /**
   * 处理窗口恢复
   */
  async handleRestoreWindow(message) {
    try {
      const { tabs, focused, incognito } = message;
      
      if (!tabs || tabs.length === 0) {
        return false;
      }
      
      // 创建新窗口
      const newWindow = await chrome.windows.create({
        url: tabs[0].url,
        focused: focused || false,
        incognito: incognito || false,
        type: 'normal'
      });
      
      // 在新窗口中恢复其他标签页
      for (let i = 1; i < tabs.length; i++) {
        const tab = tabs[i];
        await chrome.tabs.create({
          url: tab.url,
          windowId: newWindow.id,
          active: tab.active || false,
          pinned: tab.pinned || false
        });
      }
      
      console.log(`Window restored with ${tabs.length} tabs`);
      return true;
    } catch (error) {
      console.error('Failed to restore window:', error);
      return false;
    }
  }

  /**
   * 通知popup更新
   */
  notifyPopupUpdate(eventType, data) {
    // 尝试向popup发送消息
    chrome.runtime.sendMessage({
      type: 'BACKGROUND_UPDATE',
      eventType: eventType,
      data: data
    }).catch(() => {
      // popup可能没有打开，忽略错误
    });
  }

  /**
   * 获取内存使用统计
   */
  getMemoryStats() {
    return {
      windowCount: this.windowData.size,
      tabCount: this.tabData.size,
      estimatedMemorySaved: this.calculateMemorySaved()
    };
  }

  /**
   * 计算估算的内存节省
   */
  calculateMemorySaved() {
    const tabCount = this.tabData.size;
    if (tabCount === 0) return 0;
    
    // 简单估算：每个标签页约占用50-100MB内存
    // 通过插件管理可以节省大约60-80%的内存
    const avgMemoryPerTab = 75; // MB
    const savingPercentage = 0.7; // 70%
    const totalMemory = tabCount * avgMemoryPerTab;
    const savedMemory = totalMemory * savingPercentage;
    
    return Math.round(savedMemory);
  }
}

// 初始化背景服务
const backgroundService = new BackgroundService();

// 将服务实例暴露给全局作用域，便于调试
globalThis.backgroundService = backgroundService;