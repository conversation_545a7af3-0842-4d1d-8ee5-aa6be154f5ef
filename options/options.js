// Options page script
document.addEventListener('DOMContentLoaded', () => {
  console.log('Options page loaded');
  
  // 简单的选项页面功能
  const elements = {
    showFavicons: document.getElementById('showFavicons'),
    showTabCount: document.getElementById('showTabCount'),
    autoCollapse: document.getElementById('autoCollapse'),
    clearDataBtn: document.getElementById('clearDataBtn'),
    exportDataBtn: document.getElementById('exportDataBtn'),
    saveBtn: document.getElementById('saveBtn')
  };

  // 加载设置
  loadSettings();

  // 绑定事件
  elements.saveBtn.addEventListener('click', saveSettings);
  elements.clearDataBtn.addEventListener('click', clearData);
  elements.exportDataBtn.addEventListener('click', exportData);

  async function loadSettings() {
    try {
      const settings = await chrome.storage.local.get(['userSettings']);
      const userSettings = settings.userSettings || {
        showFavicons: true,
        showTabCount: true,
        autoCollapse: false
      };

      elements.showFavicons.checked = userSettings.showFavicons;
      elements.showTabCount.checked = userSettings.showTabCount;
      elements.autoCollapse.checked = userSettings.autoCollapse;
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async function saveSettings() {
    try {
      const settings = {
        showFavicons: elements.showFavicons.checked,
        showTabCount: elements.showTabCount.checked,
        autoCollapse: elements.autoCollapse.checked
      };

      await chrome.storage.local.set({ userSettings: settings });
      
      // 显示保存成功消息
      const originalText = elements.saveBtn.textContent;
      elements.saveBtn.textContent = '已保存';
      elements.saveBtn.style.backgroundColor = '#137333';
      
      setTimeout(() => {
        elements.saveBtn.textContent = originalText;
        elements.saveBtn.style.backgroundColor = '#1a73e8';
      }, 1000);
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('保存设置失败');
    }
  }

  async function clearData() {
    if (confirm('确定要清除所有数据吗？此操作无法撤销。')) {
      try {
        await chrome.storage.local.clear();
        alert('数据已清除');
      } catch (error) {
        console.error('Failed to clear data:', error);
        alert('清除数据失败');
      }
    }
  }

  async function exportData() {
    try {
      const data = await chrome.storage.local.get();
      const dataStr = JSON.stringify(data, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'chrome-tab-manager-data.json';
      a.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export data:', error);
      alert('导出数据失败');
    }
  }
});