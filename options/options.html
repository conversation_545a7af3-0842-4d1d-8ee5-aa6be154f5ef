<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome标签页管理器 - 设置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(60, 64, 67, 0.3);
    }
    h1 {
      color: #202124;
      margin-bottom: 30px;
    }
    .setting-section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e8eaed;
    }
    .setting-section:last-child {
      border-bottom: none;
    }
    .setting-title {
      font-size: 16px;
      font-weight: 500;
      color: #202124;
      margin-bottom: 10px;
    }
    .setting-description {
      font-size: 14px;
      color: #5f6368;
      margin-bottom: 15px;
    }
    .setting-control {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .btn {
      padding: 8px 16px;
      border: 1px solid #e8eaed;
      border-radius: 4px;
      background-color: white;
      cursor: pointer;
      font-size: 14px;
    }
    .btn-primary {
      background-color: #1a73e8;
      color: white;
      border-color: #1a73e8;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Chrome标签页管理器 - 设置</h1>
    
    <div class="setting-section">
      <div class="setting-title">基本设置</div>
      <div class="setting-description">配置插件的基本行为和外观</div>
      <div class="setting-control">
        <label>
          <input type="checkbox" id="showFavicons"> 显示网站图标
        </label>
      </div>
      <div class="setting-control">
        <label>
          <input type="checkbox" id="showTabCount"> 显示标签页计数
        </label>
      </div>
      <div class="setting-control">
        <label>
          <input type="checkbox" id="autoCollapse"> 自动折叠窗口组
        </label>
      </div>
    </div>

    <div class="setting-section">
      <div class="setting-title">数据管理</div>
      <div class="setting-description">管理插件存储的数据</div>
      <div class="setting-control">
        <button class="btn" id="clearDataBtn">清除所有数据</button>
        <button class="btn" id="exportDataBtn">导出数据</button>
      </div>
    </div>

    <div class="setting-section">
      <div class="setting-control">
        <button class="btn btn-primary" id="saveBtn">保存设置</button>
      </div>
    </div>
  </div>

  <script src="options.js"></script>
</body>
</html>