<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Popup尺寸测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    
    .test-frame {
      width: 420px;
      height: 600px;
      border: 2px solid #1a73e8;
      border-radius: 8px;
      background: white;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      position: relative;
      overflow: hidden;
    }
    
    .test-frame iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .test-info {
      margin-bottom: 20px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .size-indicator {
      position: absolute;
      top: -30px;
      left: 0;
      background: #1a73e8;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .test-result {
      margin-top: 20px;
      padding: 16px;
      border-radius: 8px;
    }
    
    .success {
      background: #e8f5e8;
      border: 1px solid #4caf50;
      color: #2e7d32;
    }
    
    .error {
      background: #ffebee;
      border: 1px solid #f44336;
      color: #c62828;
    }
  </style>
</head>
<body>
  <div class="test-info">
    <h1>Chrome插件Popup尺寸测试</h1>
    <p>这个页面用于测试popup的实际显示尺寸。下面的框架显示了popup应该的标准尺寸（420px × 600px）。</p>
    <p><strong>使用方法：</strong></p>
    <ol>
      <li>在Chrome中加载插件</li>
      <li>点击插件图标查看popup</li>
      <li>对比popup实际尺寸与下面的参考框架</li>
    </ol>
  </div>
  
  <div class="test-frame">
    <div class="size-indicator">420px × 600px</div>
    <iframe src="popup/popup.html"></iframe>
  </div>
  
  <div id="testResult" class="test-result" style="display: none;">
    <!-- 测试结果将在这里显示 -->
  </div>
  
  <script>
    // 简单的测试脚本
    function runSizeTest() {
      const frame = document.querySelector('.test-frame');
      const iframe = frame.querySelector('iframe');
      const resultDiv = document.getElementById('testResult');
      
      // 检查iframe是否加载成功
      iframe.onload = function() {
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          const body = iframeDoc.body;
          const html = iframeDoc.documentElement;
          
          const actualWidth = body.offsetWidth;
          const actualHeight = body.offsetHeight;
          
          let result = '';
          let isSuccess = true;
          
          if (actualWidth === 420 && actualHeight === 600) {
            result = `
              <h3>✅ 测试通过</h3>
              <p>Popup尺寸正确：${actualWidth}px × ${actualHeight}px</p>
              <p>修复成功！插件popup现在应该能正常显示。</p>
            `;
            resultDiv.className = 'test-result success';
          } else {
            result = `
              <h3>❌ 测试失败</h3>
              <p>期望尺寸：420px × 600px</p>
              <p>实际尺寸：${actualWidth}px × ${actualHeight}px</p>
              <p>请检查CSS设置或重新加载插件。</p>
            `;
            resultDiv.className = 'test-result error';
            isSuccess = false;
          }
          
          resultDiv.innerHTML = result;
          resultDiv.style.display = 'block';
          
        } catch (error) {
          resultDiv.innerHTML = `
            <h3>⚠️ 测试错误</h3>
            <p>无法访问iframe内容，可能是跨域限制。</p>
            <p>请直接在Chrome插件中测试popup显示效果。</p>
          `;
          resultDiv.className = 'test-result error';
          resultDiv.style.display = 'block';
        }
      };
      
      iframe.onerror = function() {
        resultDiv.innerHTML = `
          <h3>❌ 加载失败</h3>
          <p>无法加载popup.html文件。</p>
          <p>请确保文件路径正确。</p>
        `;
        resultDiv.className = 'test-result error';
        resultDiv.style.display = 'block';
      };
    }
    
    // 页面加载完成后运行测试
    window.addEventListener('load', runSizeTest);
    
    // 添加手动测试按钮
    const testButton = document.createElement('button');
    testButton.textContent = '重新测试';
    testButton.style.cssText = `
      margin-top: 10px;
      padding: 8px 16px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    `;
    testButton.onclick = runSizeTest;
    document.querySelector('.test-info').appendChild(testButton);
  </script>
</body>
</html>
